#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试章节信息显示功能
"""

import requests
import json

def test_web_chapter_display():
    """测试Web界面的章节信息显示"""
    base_url = "http://localhost:5001"
    
    print("=" * 60)
    print("测试Web界面章节信息显示")
    print("=" * 60)
    
    session = requests.Session()
    
    try:
        # 登录
        login_data = {'user_id': '测试'}
        response = session.post(f"{base_url}/login", data=login_data)
        if response.status_code != 200:
            print(f"❌ 登录失败: {response.status_code}")
            return
        
        print("✅ 登录成功")
        
        # 开始阅读夏洛的网
        book_path = "1_书库_Library/夏洛的网.epub"
        start_data = {'book_path': book_path}
        
        response = session.post(
            f"{base_url}/api/start_book",
            json=start_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code != 200:
            print(f"❌ 开始阅读失败: {response.status_code}")
            return
        
        data = response.json()
        if not data.get('success'):
            print(f"❌ 书籍加载失败: {data.get('error')}")
            return
        
        book_id = data['book_id']
        print(f"✅ 书籍加载成功: {data['book_info']['title']}")
        print(f"📊 总文本块数: {data['book_info']['total_blocks']}")
        
        # 测试前5个文本块的章节信息
        print(f"\n📋 测试前5个文本块的章节信息:")
        
        for i in range(5):
            response = session.get(f"{base_url}/api/reading/{book_id}/{i}")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    block_info = data.get('block_info', {})
                    chapter_info = block_info.get('chapter_info')
                    
                    print(f"\n--- 文本块 {i+1} ---")
                    print(f"📝 模式: {data['mode']}")
                    print(f"📊 内容长度: {len(data['content'])} 字符")
                    
                    if chapter_info:
                        print(f"📚 章节标题: {chapter_info.get('chapter_title', '未知')}")
                        print(f"📖 章节编号: {chapter_info.get('chapter_number', '未知')}")
                        print(f"🔗 完整章节: {chapter_info.get('is_complete_chapter', False)}")
                        
                        if not chapter_info.get('is_complete_chapter', True):
                            print(f"📄 部分标题: {chapter_info.get('part_title', '未知')}")
                            print(f"🔢 部分编号: {chapter_info.get('part_number', '未知')}")
                    else:
                        print("❌ 无章节信息")
                    
                    # 显示内容预览
                    content_preview = data['content'][:100].replace('\n', ' ')
                    print(f"📄 内容预览: {content_preview}...")
                else:
                    print(f"❌ 获取文本块 {i+1} 失败: {data.get('error')}")
            else:
                print(f"❌ API调用失败: {response.status_code}")
        
        print(f"\n✅ 章节信息显示测试完成!")
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()


def test_chapter_navigation():
    """测试章节导航功能"""
    base_url = "http://localhost:5001"
    
    print(f"\n" + "=" * 60)
    print("测试章节导航功能")
    print("=" * 60)
    
    session = requests.Session()
    
    try:
        # 登录
        login_data = {'user_id': '测试'}
        session.post(f"{base_url}/login", data=login_data)
        
        # 开始阅读
        book_path = "1_书库_Library/夏洛的网.epub"
        start_data = {'book_path': book_path}
        response = session.post(
            f"{base_url}/api/start_book",
            json=start_data,
            headers={'Content-Type': 'application/json'}
        )
        
        data = response.json()
        book_id = data['book_id']
        
        # 测试跳转到不同章节
        test_blocks = [0, 5, 10, 15, 20]  # 测试几个不同的文本块
        
        print(f"📋 测试章节跳转:")
        
        for block_index in test_blocks:
            response = session.get(f"{base_url}/api/reading/{book_id}/{block_index}")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    chapter_info = data.get('block_info', {}).get('chapter_info')
                    progress = data.get('progress', {})
                    
                    print(f"\n🔗 跳转到文本块 {block_index + 1}:")
                    if chapter_info:
                        print(f"  📚 章节: {chapter_info.get('chapter_title', '未知')}")
                    print(f"  📊 进度: {progress.get('progress_percentage', 0)}%")
                    print(f"  📝 模式: {data['mode']}")
        
        print(f"\n✅ 章节导航测试完成!")
        
    except Exception as e:
        print(f"❌ 导航测试异常: {e}")


if __name__ == "__main__":
    test_web_chapter_display()
    test_chapter_navigation()
