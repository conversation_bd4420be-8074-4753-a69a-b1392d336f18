#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据迁移脚本
从原有的YAML+Markdown格式迁移到新的JSON格式
"""

from utils.helpers import save_json, load_yaml, safe_filename
from config.settings import load_config
import os
import sys
import yaml
import json
import re
import time
from datetime import datetime
from typing import Dict, Any, List, Optional

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def parse_yaml_frontmatter(file_path: str) -> tuple[Dict[str, Any], str]:
    """解析YAML Front Matter文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 检查是否有YAML Front Matter
        if content.startswith('---'):
            # 找到第二个---的位置
            first_end = content.find('\n---\n', 4)  # 从第4个字符开始查找
            if first_end == -1:
                first_end = content.find('\n---', 4)

            if first_end != -1:
                yaml_content = content[4:first_end].strip()  # 跳过开头的---
                markdown_content = content[first_end+4:].strip()  # 跳过结尾的---
            else:
                # 回退到原来的分割方法
                parts = content.split('---', 2)
                if len(parts) >= 3:
                    yaml_content = parts[1].strip()
                    markdown_content = parts[2].strip()
                else:
                    return {}, content

            # 清理YAML内容中的注释行（以#开头的行）
            yaml_lines = []
            for line in yaml_content.split('\n'):
                # 保留非注释行，但移除行内注释
                if line.strip() and not line.strip().startswith('#'):
                    # 移除行内注释
                    if '#' in line:
                        line = line.split('#')[0].rstrip()
                    yaml_lines.append(line)

            cleaned_yaml = '\n'.join(yaml_lines)

            # 解析YAML
            yaml_data = yaml.safe_load(cleaned_yaml)
            return yaml_data or {}, markdown_content

        # 如果没有YAML Front Matter，尝试直接解析为YAML
        try:
            yaml_data = yaml.safe_load(content)
            return yaml_data or {}, ""
        except:
            return {}, content

    except Exception as e:
        print(f"解析文件失败 {file_path}: {e}")
        return {}, ""


def migrate_main_config() -> Dict[str, Any]:
    """迁移主配置文件"""
    print("迁移主配置文件...")

    yaml_data, markdown_content = parse_yaml_frontmatter("柔性阅读计划.md")

    # 提取用户配置
    users_data = {}
    if 'userProfiles' in yaml_data:
        for user_profile in yaml_data['userProfiles']:
            username = user_profile['username']

            # 转换用户状态格式
            user_state = {
                'username': username,
                'createdAt': time.time(),
                'lastActivity': parse_timestamp(user_profile.get('currentState', {}).get('lastActivity')),
                'interface': None,
                'cognitiveRadar': user_profile.get('cognitiveRadar', {}),
                'currentState': {
                    'currentBook': user_profile.get('currentState', {}).get('currentBook'),
                    'readingState': user_profile.get('currentState', {}).get('readingState', 'not_started'),
                    'progressPointer': {
                        'currentMode': user_profile.get('currentState', {}).get('progressPointer', {}).get('currentMode', 'ai_summary'),
                        'lastTextBlockHash': user_profile.get('currentState', {}).get('progressPointer', {}).get('lastTextBlockHash'),
                        'lastPosition': 0
                    }
                },
                'readHistory': []
            }

            users_data[username] = user_state

    return users_data


def parse_timestamp(timestamp_str: Optional[str]) -> Optional[float]:
    """解析时间戳字符串为Unix时间戳"""
    if not timestamp_str:
        return None

    try:
        # 尝试解析 "2025-07-21 20:20:00" 格式
        dt = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")
        return dt.timestamp()
    except:
        return None


def migrate_book_analysis() -> Dict[str, Any]:
    """迁移书籍分析数据"""
    print("迁移书籍分析数据...")

    analysis_dir = "2_分析_Analysis"
    books_data = {}

    if not os.path.exists(analysis_dir):
        return books_data

    for filename in os.listdir(analysis_dir):
        if filename.endswith('_连接点.md') and filename != 'TEMPLATE_书籍分析报告.md':
            file_path = os.path.join(analysis_dir, filename)

            # 提取书名
            book_name = filename.replace('_连接点.md', '')

            # 解析分析文件
            yaml_data, markdown_content = parse_yaml_frontmatter(file_path)

            if yaml_data:
                # 转换为新格式
                book_analysis = {
                    'bookTitle': yaml_data.get('bookTitle', book_name),
                    'author': yaml_data.get('author', ''),
                    'language': yaml_data.get('language', '中文'),
                    'category': yaml_data.get('category', ''),
                    'cognitiveDomains': yaml_data.get('cognitiveDomains', {}),
                    'estimatedReadingTime': yaml_data.get('estimatedReadingTime', ''),
                    'difficultyLevel': yaml_data.get('difficultyLevel', ''),
                    'coreThemes': yaml_data.get('coreThemes', []),
                    'keyConcepts': yaml_data.get('keyConcepts', []),
                    'cognitiveScaffolding': yaml_data.get('cognitiveScaffolding', {}),
                    'categorySpecificData': yaml_data.get('categorySpecificData', {}),
                    'potentialDiscussionPoints': yaml_data.get('potentialDiscussionPoints', []),
                    'markdownContent': markdown_content,
                    'migratedAt': time.time()
                }

                books_data[book_name] = book_analysis
                print(f"  迁移书籍分析: {book_name}")

    return books_data


def migrate_user_logs() -> Dict[str, List[Dict[str, Any]]]:
    """迁移用户日志数据"""
    print("迁移用户日志数据...")

    logs_dir = "3_日志_Log"
    users_logs = {}

    if not os.path.exists(logs_dir):
        return users_logs

    for filename in os.listdir(logs_dir):
        if filename.endswith('_状态日志.md'):
            username = filename.replace('_状态日志.md', '')
            file_path = os.path.join(logs_dir, filename)

            # 解析日志文件
            logs = parse_log_file(file_path)
            users_logs[username] = logs

            print(f"  迁移用户日志: {username} ({len(logs)} 条记录)")

    return users_logs


def parse_log_file(file_path: str) -> List[Dict[str, Any]]:
    """解析日志文件"""
    logs = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 按 --- 分割日志条目
        entries = re.split(r'\n---\n', content)

        for entry in entries:
            entry = entry.strip()
            if not entry or entry.startswith('#'):
                continue

            # 解析YAML部分
            try:
                yaml_data = yaml.safe_load(entry)
                if yaml_data and isinstance(yaml_data, dict):
                    # 转换时间戳
                    if 'timestamp' in yaml_data:
                        yaml_data['timestamp'] = parse_timestamp(
                            yaml_data['timestamp'])

                    logs.append(yaml_data)
            except:
                continue

    except Exception as e:
        print(f"解析日志文件失败 {file_path}: {e}")

    return logs


def save_migrated_data(users_data: Dict[str, Any], books_data: Dict[str, Any], users_logs: Dict[str, List[Dict[str, Any]]]):
    """保存迁移后的数据"""
    print("保存迁移后的数据...")

    # 确保目录存在
    os.makedirs("data/logs", exist_ok=True)
    os.makedirs("data/analysis", exist_ok=True)
    os.makedirs("data/backups", exist_ok=True)

    # 保存用户状态
    for username, user_data in users_data.items():
        user_file = f"data/logs/{username}_state.json"
        save_json(user_file, user_data)
        print(f"  保存用户状态: {user_file}")

    # 保存书籍分析
    for book_name, book_data in books_data.items():
        safe_book_name = safe_filename(book_name)
        book_file = f"data/analysis/{safe_book_name}_analysis.json"
        save_json(book_file, book_data)
        print(f"  保存书籍分析: {book_file}")

    # 保存用户日志历史
    for username, logs in users_logs.items():
        if logs:
            logs_file = f"data/logs/{username}_history.json"
            save_json(logs_file, logs)
            print(f"  保存用户日志历史: {logs_file}")

    # 创建迁移报告
    migration_report = {
        'migrationDate': time.time(),
        'migratedUsers': list(users_data.keys()),
        'migratedBooks': list(books_data.keys()),
        'migratedLogs': {username: len(logs) for username, logs in users_logs.items()},
        'sourceFiles': {
            'mainConfig': '柔性阅读计划.md',
            'analysisDir': '2_分析_Analysis/',
            'logsDir': '3_日志_Log/'
        },
        'targetFormat': 'JSON',
        'backwardCompatible': True
    }

    save_json("data/migration_report.json", migration_report)
    print(f"  保存迁移报告: data/migration_report.json")


def main():
    """主迁移函数"""
    print("=" * 60)
    print("柔性阅读计划 - 数据迁移工具")
    print("从 YAML+Markdown 格式迁移到 JSON 格式")
    print("=" * 60)

    try:
        # 检查源文件
        if not os.path.exists("柔性阅读计划.md"):
            print("错误: 未找到源配置文件 '柔性阅读计划.md'")
            return

        # 执行迁移
        users_data = migrate_main_config()
        books_data = migrate_book_analysis()
        users_logs = migrate_user_logs()

        # 保存数据
        save_migrated_data(users_data, books_data, users_logs)

        # 输出统计信息
        print("\n" + "=" * 60)
        print("迁移完成！")
        print("=" * 60)
        print(f"迁移用户数: {len(users_data)}")
        print(f"迁移书籍分析: {len(books_data)}")
        print(f"迁移日志记录: {sum(len(logs) for logs in users_logs.values())}")
        print("\n新系统现在可以使用迁移后的数据。")
        print("原始文件已保留，具备向后兼容性。")

    except Exception as e:
        print(f"迁移失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
