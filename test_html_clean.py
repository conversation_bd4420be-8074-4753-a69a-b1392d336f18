#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试HTML清理功能
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_html_cleaning():
    """测试HTML清理功能"""
    from core.document_parser import extract_text_from_html
    
    # 测试包含&#13;的HTML内容
    test_html = """
    <p>1 早饭前&#13; "爸爸拿着那把斧子去哪儿？"摆桌子吃早饭的时候，弗恩问她妈妈。&#13;</p>
    <p>"去猪圈，"阿拉布尔太太回答说，"昨天夜里下小猪了。"&#13;</p>
    """
    
    print("原始HTML:")
    print(repr(test_html))
    print("\n清理后的文本:")
    
    cleaned_text = extract_text_from_html(test_html)
    print(repr(cleaned_text))
    print("\n显示效果:")
    print(cleaned_text)

if __name__ == "__main__":
    test_html_cleaning()
