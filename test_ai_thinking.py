#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试AI思考模式过滤
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_thinking_filter():
    """测试思考内容过滤"""
    from core.ai_client import filter_thinking_content
    
    # 测试包含思考标签的内容
    test_content = """<think>
这是一段思考内容，应该被过滤掉。
用户要求精炼转述，我需要分析原文...
</think>

这是实际的回答内容，应该保留。

<thinking>
另一种思考标签格式
</thinking>

这是另一段有用的内容。"""
    
    print("原始内容:")
    print(repr(test_content))
    print("\n过滤后的内容:")
    
    filtered_content = filter_thinking_content(test_content)
    print(repr(filtered_content))
    print("\n显示效果:")
    print(filtered_content)

def test_ai_call():
    """测试AI调用（如果AI服务可用）"""
    from core.ai_client import call_local_ai_with_retry, is_ai_available
    
    print("\n" + "="*50)
    print("测试AI调用")
    print("="*50)
    
    # 检查AI是否可用
    if not is_ai_available():
        print("AI服务不可用，跳过测试")
        return
    
    # 测试简单的AI调用
    prompt = "请用一句话总结：小猪威尔伯是一只可爱的小猪。"
    
    print(f"发送提示: {prompt}")
    print("等待AI响应...")
    
    response = call_local_ai_with_retry(prompt)
    
    if response:
        print(f"AI响应: {response}")
        print(f"响应长度: {len(response)} 字符")
        
        # 检查是否包含思考标签
        if '<think>' in response or '<thinking>' in response:
            print("⚠️  警告: 响应中仍包含思考标签")
        else:
            print("✓ 响应中没有思考标签")
    else:
        print("AI调用失败")

if __name__ == "__main__":
    test_thinking_filter()
    test_ai_call()
