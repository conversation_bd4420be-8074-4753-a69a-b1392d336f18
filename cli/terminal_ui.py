#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
命令行界面模块
"""

import os
import sys
from typing import Dict, Any, List, Optional

from core.user_manager import (
    load_user_state, save_user_state, backup_user_data, list_available_users
)
from core.ai_client import is_ai_available
from core.reading_engine import ReadingEngine
from utils.helpers import format_file_size


def print_banner():
    """打印程序横幅"""
    print("=" * 60)
    print("           柔性阅读计划 v6.0")
    print("        个性化阅读平台 - 命令行界面")
    print("=" * 60)
    print()


def authenticate_user(config: Dict[str, Any]) -> Optional[str]:
    """用户身份认证"""
    print("您好！为了给您提供最合适的帮助，请问现在是哪位用户在与我交流？")
    print()

    # 显示可用用户
    available_users = list_available_users(config)
    default_users = ["老汉", "啾啾", "妙妙", "测试"]

    all_users = list(set(default_users + available_users))

    for i, user in enumerate(all_users, 1):
        print(f"{i}. {user}")

    print(f"{len(all_users) + 1}. 创建新用户")
    print("0. 退出")
    print()

    while True:
        try:
            choice = input("请选择 (输入数字): ").strip()

            if choice == "0":
                return None

            choice_num = int(choice)

            if 1 <= choice_num <= len(all_users):
                return all_users[choice_num - 1]
            elif choice_num == len(all_users) + 1:
                # 创建新用户
                new_user = input("请输入新用户名: ").strip()
                if new_user:
                    return new_user
                else:
                    print("用户名不能为空，请重新选择。")
            else:
                print("无效选择，请重新输入。")

        except ValueError:
            print("请输入有效的数字。")
        except KeyboardInterrupt:
            print("\n\n再见！")
            return None


def admin_mode(config: Dict[str, Any]) -> None:
    """老汉管理模式"""
    print("\n" + "=" * 40)
    print("         管理员模式")
    print("=" * 40)

    while True:
        print("\n管理选项:")
        print("1. 查看所有用户状态")
        print("2. 查看系统配置")
        print("3. 检查AI服务状态")
        print("4. 数据备份管理")
        print("0. 返回主菜单")

        choice = input("\n请选择: ").strip()

        if choice == "0":
            break
        elif choice == "1":
            show_all_users_status(config)
        elif choice == "2":
            show_system_config(config)
        elif choice == "3":
            check_ai_status(config)
        elif choice == "4":
            backup_management(config)
        else:
            print("无效选择，请重新输入。")


def show_all_users_status(config: Dict[str, Any]) -> None:
    """显示所有用户状态"""
    print("\n" + "-" * 40)
    print("用户状态概览")
    print("-" * 40)

    users = list_available_users(config)

    if not users:
        print("暂无用户数据。")
        return

    for user_id in users:
        state = load_user_state(user_id, config)
        current_book = state["currentState"]["currentBook"]
        reading_state = state["currentState"]["readingState"]

        print(f"\n用户: {user_id}")
        print(f"  当前书籍: {current_book or '无'}")
        print(f"  阅读状态: {reading_state}")
        print(f"  已读书籍: {len(state.get('readHistory', []))}")


def show_system_config(config: Dict[str, Any]) -> None:
    """显示系统配置"""
    print("\n" + "-" * 40)
    print("系统配置")
    print("-" * 40)

    print(f"版本: {config['system']['version']}")
    print(f"数据目录: {config['system']['data_dir']}")
    print(f"AI服务地址: {config['ai']['api_url']}")
    print(f"AI模型: {config['ai']['model']}")
    print(f"默认阅读模式: {config['reading']['default_mode']}")


def check_ai_status(config: Dict[str, Any]) -> None:
    """检查AI服务状态"""
    print("\n" + "-" * 40)
    print("AI服务状态检查")
    print("-" * 40)

    api_url = config["ai"]["api_url"]
    print(f"正在检查 {api_url} ...")

    if is_ai_available(api_url):
        print("✓ AI服务正常运行")
    else:
        print("✗ AI服务不可用")
        print("  请检查LM Studio是否正在运行")


def backup_management(config: Dict[str, Any]) -> None:
    """备份管理"""
    print("\n" + "-" * 40)
    print("数据备份管理")
    print("-" * 40)

    users = list_available_users(config)

    if not users:
        print("暂无用户数据需要备份。")
        return

    print("选择要备份的用户:")
    for i, user in enumerate(users, 1):
        print(f"{i}. {user}")
    print("0. 备份所有用户")

    choice = input("\n请选择: ").strip()

    try:
        if choice == "0":
            # 备份所有用户
            for user in users:
                backup_user_data(user, config)
        else:
            choice_num = int(choice)
            if 1 <= choice_num <= len(users):
                user = users[choice_num - 1]
                backup_user_data(user, config)
            else:
                print("无效选择。")
    except ValueError:
        print("请输入有效的数字。")


def reading_mode(user_id: str, config: Dict[str, Any]) -> None:
    """用户阅读模式"""
    print(f"\n欢迎回来，{user_id}！")

    # 初始化阅读引擎
    reading_engine = ReadingEngine(config)

    # 加载用户状态
    state = load_user_state(user_id, config)

    # 设置界面类型
    state["interface"] = "cli"
    save_user_state(user_id, state, config)

    # 检查是否有正在阅读的书籍
    current_book = state["currentState"]["currentBook"]
    reading_state = state["currentState"]["readingState"]

    if current_book and reading_state == "in_progress":
        print(f"检测到您正在阅读《{current_book}》，是否继续？")
        print("1. 继续阅读")
        print("2. 选择新书")
        print("0. 注销")

        choice = input("\n请选择: ").strip()

        if choice == "1":
            # 实现断点续读
            resume_reading(user_id, current_book, reading_engine, config)
        elif choice == "2":
            # 实现书籍选择
            select_and_read_book(user_id, reading_engine, config)
        else:
            logout_user(user_id, config)
            return
    else:
        # 新用户流程 - 选择书籍
        select_and_read_book(user_id, reading_engine, config)

    # 用户注销时备份数据
    logout_user(user_id, config)


def logout_user(user_id: str, config: Dict[str, Any]) -> None:
    """用户注销"""
    print(f"\n{user_id}，感谢您的使用！")

    # 备份用户数据
    backup_user_data(user_id, config)

    print("数据已保存，再见！")


def select_and_read_book(user_id: str, reading_engine: ReadingEngine, config: Dict[str, Any]) -> None:
    """选择并开始阅读书籍"""
    print("\n" + "=" * 40)
    print("书籍选择")
    print("=" * 40)

    # 查找可用书籍
    books_dirs = [
        os.path.join(config["system"]["data_dir"], "books"),
        "1_书库_Library",  # 兼容原目录结构
        "data/books"
    ]

    books_dir = None
    for dir_path in books_dirs:
        if os.path.exists(dir_path):
            books_dir = dir_path
            break

    available_books = []
    if books_dir and os.path.exists(books_dir):
        for filename in os.listdir(books_dir):
            file_path = os.path.join(books_dir, filename)
            if os.path.isfile(file_path):
                file_ext = os.path.splitext(filename)[1].lower()
                if file_ext in config["reading"]["supported_formats"]:
                    available_books.append(file_path)

    if not available_books:
        print("未找到可用的书籍文件。")
        print(f"搜索目录: {books_dirs}")
        print("请将书籍文件放入以上任一目录。")
        return

    # 显示书籍列表
    print(f"找到 {len(available_books)} 本书籍:")
    for i, book_path in enumerate(available_books, 1):
        filename = os.path.basename(book_path)
        file_size = format_file_size(os.path.getsize(book_path))
        print(f"{i}. {filename} ({file_size})")

    print("0. 返回")

    # 选择书籍
    while True:
        try:
            choice = input("\n请选择要阅读的书籍: ").strip()

            if choice == "0":
                return

            book_index = int(choice) - 1
            if 0 <= book_index < len(available_books):
                selected_book = available_books[book_index]
                start_reading_book(user_id, selected_book,
                                   reading_engine, config)
                break
            else:
                print("无效的书籍编号，请重新选择。")

        except ValueError:
            print("请输入有效的数字。")
        except KeyboardInterrupt:
            print("\n返回主菜单")
            return


def resume_reading(user_id: str, book_id: str, reading_engine: ReadingEngine, config: Dict[str, Any]) -> None:
    """断点续读"""
    # 查找书籍文件
    book_path = find_book_file(book_id, config)
    if not book_path:
        print(f"未找到书籍文件: {book_id}")
        print("请重新选择书籍。")
        select_and_read_book(user_id, reading_engine, config)
        return

    try:
        # 加载书籍
        book_data = reading_engine.load_book(book_path)

        # 获取阅读进度
        progress = reading_engine.get_reading_progress(user_id, book_data)
        current_position = progress['current_position']

        print(f"\n继续阅读《{book_data.get('title', book_id)}》")
        print(
            f"当前进度: {progress['progress_percentage']}% ({current_position + 1}/{progress['total_blocks']})")

        # 开始阅读循环
        reading_loop(user_id, book_data, current_position,
                     reading_engine, config)

    except Exception as e:
        print(f"加载书籍失败: {str(e)}")


def start_reading_book(user_id: str, book_path: str, reading_engine: ReadingEngine, config: Dict[str, Any]) -> None:
    """开始阅读新书籍"""
    try:
        print(f"\n正在加载书籍: {os.path.basename(book_path)}")

        # 加载书籍
        book_data = reading_engine.load_book(book_path)

        # 显示书籍信息
        book_info = reading_engine.get_book_info(book_data)
        print(f"\n书籍信息:")
        print(f"标题: {book_info['title']}")
        print(f"作者: {book_info['author']}")
        print(f"格式: {book_info['format']}")
        print(f"文件大小: {format_file_size(book_info['file_size'])}")
        print(f"文本块数: {book_info['total_blocks']}")
        print(f"预计阅读时间: {book_info['statistics']['reading_time_minutes']} 分钟")

        # 确认开始阅读
        confirm = input("\n开始阅读这本书吗？(y/n): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            return

        # 从第一个文本块开始阅读
        reading_loop(user_id, book_data, 0, reading_engine, config)

    except Exception as e:
        print(f"加载书籍失败: {str(e)}")


def reading_loop(user_id: str, book_data: Dict[str, Any], start_position: int, reading_engine: ReadingEngine, config: Dict[str, Any]) -> None:
    """主要阅读循环"""
    current_position = start_position

    while True:
        try:
            # 获取当前文本块
            result = reading_engine.navigate_to_block(
                user_id, book_data, current_position)

            if result['mode'] == 'end_of_book':
                print("\n" + "=" * 50)
                print("🎉 恭喜！您已完成这本书的阅读！")
                print("=" * 50)

                # 标记书籍为已完成
                from core.user_manager import mark_book_as_finished
                mark_book_as_finished(
                    user_id, book_data['book_id'], book_data, config)

                input("\n按回车键继续...")
                break

            # 显示文本内容
            display_text_block(result, current_position + 1,
                               len(book_data['text_blocks']))

            # 获取并显示菜单
            menu_options = reading_engine.get_reading_menu(
                user_id, book_data, current_position)
            choice = display_reading_menu(menu_options)

            # 处理用户选择
            if choice == 'next':
                current_position += 1
            elif choice == 'previous':
                current_position = max(0, current_position - 1)
            elif choice == 'switch_original':
                reading_engine.switch_reading_mode(user_id, 'original_text')
            elif choice == 'switch_summary':
                reading_engine.switch_reading_mode(user_id, 'ai_summary')
            elif choice == 'progress':
                show_reading_progress(user_id, book_data, reading_engine)
            elif choice == 'book_info':
                show_book_info(book_data, reading_engine)
            elif choice == 'exit':
                print("\n保存阅读进度...")
                break
            else:
                print("无效选择，请重新输入。")

        except KeyboardInterrupt:
            print("\n\n保存阅读进度...")
            break
        except Exception as e:
            print(f"\n发生错误: {str(e)}")
            break


def find_book_file(book_id: str, config: Dict[str, Any]) -> Optional[str]:
    """查找书籍文件"""
    # 在多个目录中查找
    search_dirs = [
        config["system"]["data_dir"] + "/books",
        "1_书库_Library",
        "data/books"
    ]

    for search_dir in search_dirs:
        if os.path.exists(search_dir):
            for filename in os.listdir(search_dir):
                if os.path.splitext(filename)[0] == book_id:
                    return os.path.join(search_dir, filename)

    return None


def display_text_block(result: Dict[str, Any], current_num: int, total_num: int) -> None:
    """显示文本块"""
    print("\n" + "=" * 60)

    # 显示章节信息（如果有）
    block_info = result.get('block_info', {})
    chapter_info = block_info.get('chapter_info')

    if chapter_info:
        chapter_title = chapter_info.get('chapter_title', '')
        if chapter_info.get('is_complete_chapter', True):
            print(f"📚 {chapter_title}")
        else:
            part_title = chapter_info.get('part_title', chapter_title)
            print(f"📚 {part_title}")

    print(f"📖 阅读进度: {current_num}/{total_num} | 模式: {result['message']}")
    if result['fallback_level'] > 0:
        print(f"⚠️  降级提示: 降级级别 {result['fallback_level']}")
    print("=" * 60)
    print()

    # 显示文本内容
    content = result['content']
    print(content)
    print()


def display_reading_menu(menu_options: List[Dict[str, str]]) -> str:
    """显示阅读菜单并获取用户选择"""
    print("-" * 40)
    print("📋 阅读选项:")

    for i, option in enumerate(menu_options, 1):
        print(f"{i}. {option['label']}")

    print("-" * 40)

    while True:
        try:
            choice = input("请选择操作: ").strip()
            choice_num = int(choice)

            if 1 <= choice_num <= len(menu_options):
                return menu_options[choice_num - 1]['key']
            else:
                print("无效选择，请重新输入。")

        except ValueError:
            print("请输入有效的数字。")


def show_reading_progress(user_id: str, book_data: Dict[str, Any], reading_engine: ReadingEngine) -> None:
    """显示阅读进度"""
    progress = reading_engine.get_reading_progress(user_id, book_data)

    print("\n" + "-" * 30)
    print("📊 阅读进度")
    print("-" * 30)
    print(f"书籍: {progress['book_title']}")
    print(
        f"当前位置: {progress['current_position'] + 1}/{progress['total_blocks']}")
    print(f"完成度: {progress['progress_percentage']}%")
    print(f"阅读模式: {progress['reading_mode']}")

    input("\n按回车键继续...")


def show_book_info(book_data: Dict[str, Any], reading_engine: ReadingEngine) -> None:
    """显示书籍详细信息"""
    book_info = reading_engine.get_book_info(book_data)

    print("\n" + "-" * 30)
    print("📚 书籍信息")
    print("-" * 30)
    print(f"标题: {book_info['title']}")
    print(f"作者: {book_info['author']}")
    print(f"格式: {book_info['format']}")
    print(f"文件大小: {format_file_size(book_info['file_size'])}")
    print(f"文本块数: {book_info['total_blocks']}")

    stats = book_info['statistics']
    print(f"字符数: {stats['char_count']}")
    print(f"词数: {stats['word_count']}")
    print(f"段落数: {stats['paragraph_count']}")
    print(f"预计阅读时间: {stats['reading_time_minutes']} 分钟")
    print(f"主要语言: {stats['language']}")

    if stats['keywords']:
        print(f"关键词: {', '.join(stats['keywords'][:5])}")

    input("\n按回车键继续...")


def start_cli_interface(config: Dict[str, Any]) -> None:
    """启动命令行界面"""
    print_banner()

    try:
        while True:
            # 用户身份认证
            user_id = authenticate_user(config)

            if user_id is None:
                print("再见！")
                break

            # 根据用户类型进入不同模式
            if user_id == "老汉":
                admin_mode(config)
            else:
                reading_mode(user_id, config)

    except KeyboardInterrupt:
        print("\n\n程序已退出，再见！")
    except Exception as e:
        print(f"\n发生错误: {e}")
        print("程序已退出。")
