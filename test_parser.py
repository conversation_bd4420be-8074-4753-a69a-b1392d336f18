#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试文档解析器
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from reading_plan.config.settings import load_config
from reading_plan.core.document_parser import (
    parse_document, get_supported_formats, validate_document
)
from reading_plan.utils.helpers import format_file_size


def test_parser():
    """测试解析器功能"""
    print("=" * 60)
    print("文档解析器测试")
    print("=" * 60)
    
    # 加载配置
    config = load_config("config.yaml")
    
    # 显示支持的格式
    supported_formats = get_supported_formats()
    print(f"支持的格式: {', '.join(supported_formats)}")
    print()
    
    # 查找测试文件
    test_files = []
    
    # 检查书库目录
    books_dir = "1_书库_Library"
    if os.path.exists(books_dir):
        for filename in os.listdir(books_dir):
            file_path = os.path.join(books_dir, filename)
            if os.path.isfile(file_path):
                test_files.append(file_path)
    
    # 检查当前目录的文本文件
    for filename in os.listdir("."):
        if filename.endswith(('.txt', '.md')):
            test_files.append(filename)
    
    if not test_files:
        print("未找到测试文件")
        return
    
    print(f"找到 {len(test_files)} 个文件:")
    for i, file_path in enumerate(test_files, 1):
        file_size = format_file_size(os.path.getsize(file_path))
        print(f"{i}. {os.path.basename(file_path)} ({file_size})")
    
    print()
    
    # 选择文件进行测试
    while True:
        try:
            choice = input("请选择要测试的文件编号 (0退出): ").strip()
            
            if choice == "0":
                break
            
            file_index = int(choice) - 1
            if 0 <= file_index < len(test_files):
                test_file = test_files[file_index]
                test_single_file(test_file, config)
            else:
                print("无效的文件编号")
                
        except ValueError:
            print("请输入有效的数字")
        except KeyboardInterrupt:
            print("\n测试结束")
            break


def test_single_file(file_path: str, config: dict):
    """测试单个文件"""
    print("\n" + "=" * 40)
    print(f"测试文件: {os.path.basename(file_path)}")
    print("=" * 40)
    
    # 验证文档
    is_valid, message = validate_document(file_path)
    print(f"文档验证: {'✓' if is_valid else '✗'} {message}")
    
    if not is_valid:
        return
    
    try:
        # 解析文档
        print("正在解析文档...")
        result = parse_document(file_path, config)
        
        # 显示解析结果
        print(f"标题: {result.get('title', '未知')}")
        print(f"作者: {result.get('author', '未知')}")
        print(f"格式: {result.get('file_format', '未知')}")
        print(f"文件大小: {format_file_size(result.get('file_size', 0))}")
        
        # 显示内容统计
        text_blocks = result.get('text_blocks', [])
        print(f"文本块数量: {len(text_blocks)}")
        
        if text_blocks:
            total_chars = sum(block['char_count'] for block in text_blocks)
            print(f"总字符数: {total_chars}")
            
            # 显示前几个文本块
            print("\n前3个文本块预览:")
            for i, block in enumerate(text_blocks[:3], 1):
                preview = block['text'][:100] + "..." if len(block['text']) > 100 else block['text']
                print(f"  块{i} ({block['char_count']}字符): {preview}")
        
        # 显示元数据
        metadata = result.get('metadata', {})
        if metadata:
            print(f"\n元数据: {metadata}")
        
        print("解析完成!")
        
    except Exception as e:
        print(f"解析失败: {str(e)}")


if __name__ == "__main__":
    test_parser()
