#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试Web API功能
"""

import requests
import json

def test_web_api():
    """测试Web API"""
    base_url = "http://localhost:5001"
    
    print("=" * 50)
    print("测试Web API功能")
    print("=" * 50)
    
    # 测试系统状态
    print("\n1. 测试系统状态API")
    try:
        response = requests.get(f"{base_url}/api/status")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ 系统状态: {data['system']}")
            print(f"✓ AI可用性: {data['ai_available']}")
            print(f"✓ 版本: {data['version']}")
        else:
            print(f"✗ 状态检查失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 状态检查异常: {e}")
    
    # 测试书籍列表API（需要登录）
    print("\n2. 测试书籍列表API")
    session = requests.Session()
    
    # 先登录
    try:
        login_data = {'user_id': '测试'}
        response = session.post(f"{base_url}/login", data=login_data)
        if response.status_code == 200:
            print("✓ 登录成功")
            
            # 获取书籍列表
            response = session.get(f"{base_url}/api/books")
            if response.status_code == 200:
                data = response.json()
                books = data.get('books', [])
                print(f"✓ 找到 {len(books)} 本书籍")
                for book in books[:3]:  # 显示前3本
                    print(f"  - {book['filename']} ({book['size']})")
            else:
                print(f"✗ 获取书籍列表失败: {response.status_code}")
        else:
            print(f"✗ 登录失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 书籍列表测试异常: {e}")
    
    # 测试开始阅读API
    print("\n3. 测试开始阅读API")
    try:
        # 使用一个已知的书籍路径
        book_path = "1_书库_Library/夏洛的网.epub"
        start_data = {'book_path': book_path}
        
        response = session.post(
            f"{base_url}/api/start_book",
            json=start_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✓ 书籍加载成功: {data['book_info']['title']}")
                print(f"✓ 作者: {data['book_info']['author']}")
                print(f"✓ 文本块数: {data['book_info']['total_blocks']}")
                
                book_id = data['book_id']
                
                # 测试获取文本块
                print("\n4. 测试获取文本块API")
                response = session.get(f"{base_url}/api/reading/{book_id}/0")
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        print(f"✓ 文本块获取成功")
                        print(f"✓ 模式: {data['mode']}")
                        print(f"✓ 内容长度: {len(data['content'])} 字符")
                        print(f"✓ 进度: {data['progress']['progress_percentage']}%")
                        print(f"✓ 菜单选项数: {len(data['menu_options'])}")
                    else:
                        print(f"✗ 文本块获取失败: {data.get('error')}")
                else:
                    print(f"✗ 文本块API失败: {response.status_code}")
            else:
                print(f"✗ 书籍加载失败: {data.get('error')}")
        else:
            print(f"✗ 开始阅读API失败: {response.status_code}")
            print(f"响应内容: {response.text}")
    except Exception as e:
        print(f"✗ 开始阅读测试异常: {e}")
    
    print("\n" + "=" * 50)
    print("Web API测试完成")
    print("=" * 50)

if __name__ == "__main__":
    test_web_api()
