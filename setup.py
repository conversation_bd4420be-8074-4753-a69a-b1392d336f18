#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

setup(
    name="reading-plan",
    version="6.0.0",
    author="老汉",
    description="柔性阅读计划 - 个性化阅读平台",
    long_description=long_description,
    long_description_content_type="text/markdown",
    packages=[".", "config", "core", "cli", "web", "utils"],
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Education",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
    python_requires=">=3.9",
    install_requires=[
        "flask>=2.3.3",
        "requests>=2.31.0",
        "pyyaml>=6.0.1",
        "ebooklib>=0.18",
        "PyPDF2>=3.0.1",
        "pdfplumber>=0.9.0",
        "python-docx>=0.8.11",
        "watchdog>=3.0.0",
    ],
    entry_points={
        "console_scripts": [
            "reading-plan=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        ".": ["web/templates/*", "web/static/*"],
    },
)
