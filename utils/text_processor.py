#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
文本处理工具模块
"""

import re
import math
from typing import List, Dict, Any, Optional, Tuple


def smart_split_text(text: str, target_size: int = 2000, min_size: int = 1000) -> List[str]:
    """智能文本分割 - 按自然段落和句子边界分割"""
    if len(text) <= target_size:
        return [text]

    chunks = []
    current_chunk = ""

    # 首先按段落分割
    paragraphs = text.split('\n\n')

    for paragraph in paragraphs:
        paragraph = paragraph.strip()
        if not paragraph:
            continue

        # 如果当前块加上新段落不超过目标大小，直接添加
        if len(current_chunk) + len(paragraph) + 2 <= target_size:
            if current_chunk:
                current_chunk += "\n\n" + paragraph
            else:
                current_chunk = paragraph
        else:
            # 保存当前块（如果不为空）
            if current_chunk and len(current_chunk) >= min_size:
                chunks.append(current_chunk)
                current_chunk = paragraph
            else:
                # 当前块太小，尝试分割段落
                if current_chunk:
                    combined = current_chunk + "\n\n" + paragraph
                else:
                    combined = paragraph

                # 如果组合后的文本仍然太长，按句子分割
                if len(combined) > target_size:
                    if current_chunk:
                        chunks.append(current_chunk)

                    # 分割长段落
                    sub_chunks = split_long_paragraph(
                        paragraph, target_size, min_size)
                    chunks.extend(sub_chunks[:-1])  # 除了最后一个
                    current_chunk = sub_chunks[-1] if sub_chunks else ""
                else:
                    current_chunk = combined

    # 添加最后一个块
    if current_chunk:
        chunks.append(current_chunk)

    return chunks


def split_long_paragraph(paragraph: str, target_size: int, min_size: int) -> List[str]:
    """分割长段落"""
    if len(paragraph) <= target_size:
        return [paragraph]

    chunks = []
    current_chunk = ""

    # 按句子分割（中文和英文）
    sentences = split_into_sentences(paragraph)

    for sentence in sentences:
        sentence = sentence.strip()
        if not sentence:
            continue

        # 如果单个句子就超过目标大小，强制分割
        if len(sentence) > target_size:
            if current_chunk and len(current_chunk) >= min_size:
                chunks.append(current_chunk)
                current_chunk = ""

            # 强制按字符分割长句子
            force_chunks = force_split_text(sentence, target_size)
            chunks.extend(force_chunks[:-1])
            current_chunk = force_chunks[-1] if force_chunks else ""
        else:
            # 检查是否可以添加到当前块
            if len(current_chunk) + len(sentence) + 1 <= target_size:
                if current_chunk:
                    current_chunk += " " + sentence
                else:
                    current_chunk = sentence
            else:
                # 保存当前块并开始新块
                if current_chunk:
                    chunks.append(current_chunk)
                current_chunk = sentence

    # 添加最后一个块
    if current_chunk:
        chunks.append(current_chunk)

    return chunks


def split_into_sentences(text: str) -> List[str]:
    """将文本分割为句子"""
    # 中文句子结束标点
    chinese_endings = r'[。！？；]'
    # 英文句子结束标点
    english_endings = r'[.!?;]'

    # 组合正则表达式
    sentence_pattern = f'({chinese_endings}|{english_endings})'

    # 分割句子
    parts = re.split(sentence_pattern, text)

    sentences = []
    current_sentence = ""

    for i, part in enumerate(parts):
        if re.match(sentence_pattern, part):
            # 这是一个句子结束符
            current_sentence += part
            sentences.append(current_sentence.strip())
            current_sentence = ""
        else:
            # 这是句子内容
            current_sentence += part

    # 添加最后一个句子（如果有）
    if current_sentence.strip():
        sentences.append(current_sentence.strip())

    return [s for s in sentences if s.strip()]


def force_split_text(text: str, max_size: int) -> List[str]:
    """强制按字符数分割文本"""
    chunks = []
    start = 0

    while start < len(text):
        end = start + max_size
        if end >= len(text):
            chunks.append(text[start:])
            break

        # 尝试在合适的位置分割（避免在单词中间分割）
        chunk = text[start:end]

        # 寻找最后一个空格或标点符号
        split_pos = max(
            chunk.rfind(' '),
            chunk.rfind('，'),
            chunk.rfind('。'),
            chunk.rfind('！'),
            chunk.rfind('？'),
            chunk.rfind(';'),
            chunk.rfind(','),
            chunk.rfind('.')
        )

        if split_pos > max_size * 0.7:  # 如果分割位置不太靠前
            end = start + split_pos + 1

        chunks.append(text[start:end])
        start = end

    return chunks


def extract_keywords(text: str, max_keywords: int = 10) -> List[str]:
    """提取文本关键词（简单实现）"""
    # 移除标点符号和特殊字符
    clean_text = re.sub(r'[^\w\s]', ' ', text)

    # 分词（简单按空格分割）
    words = clean_text.split()

    # 过滤短词和常见停用词
    stop_words = {
        '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
        '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
        'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of',
        'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had'
    }

    # 统计词频
    word_freq = {}
    for word in words:
        word = word.lower().strip()
        if len(word) > 1 and word not in stop_words:
            word_freq[word] = word_freq.get(word, 0) + 1

    # 按频率排序并返回前N个
    sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
    return [word for word, freq in sorted_words[:max_keywords]]


def calculate_reading_time(text: str, wpm: int = 200) -> int:
    """计算阅读时间（分钟）"""
    # 统计字符数（中文）和单词数（英文）
    chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
    english_words = len(re.findall(r'\b[a-zA-Z]+\b', text))

    # 中文按字符计算，英文按单词计算
    # 假设中文阅读速度为每分钟300字，英文为每分钟200词
    chinese_time = chinese_chars / 300
    english_time = english_words / wpm

    total_minutes = chinese_time + english_time
    return max(1, math.ceil(total_minutes))


def detect_language(text: str) -> str:
    """检测文本主要语言"""
    # 统计不同语言字符数
    chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
    english_chars = len(re.findall(r'[a-zA-Z]', text))

    total_chars = len(text.replace(' ', '').replace('\n', ''))

    if total_chars == 0:
        return 'unknown'

    chinese_ratio = chinese_chars / total_chars
    english_ratio = english_chars / total_chars

    if chinese_ratio > 0.3:
        return 'zh'
    elif english_ratio > 0.5:
        return 'en'
    else:
        return 'mixed'


def clean_text(text: str) -> str:
    """清理文本 - 移除多余空白和特殊字符"""
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text)

    # 移除行首行尾空白
    text = text.strip()

    # 移除特殊的Unicode字符
    text = re.sub(r'[\ufeff\u200b\u200c\u200d]', '', text)

    return text


def summarize_text_stats(text: str) -> Dict[str, Any]:
    """生成文本统计信息"""
    clean_text_content = clean_text(text)

    stats = {
        'char_count': len(clean_text_content),
        'word_count': len(clean_text_content.split()),
        'paragraph_count': len([p for p in text.split('\n\n') if p.strip()]),
        'sentence_count': len(split_into_sentences(clean_text_content)),
        'language': detect_language(clean_text_content),
        'reading_time_minutes': calculate_reading_time(clean_text_content),
        'keywords': extract_keywords(clean_text_content)
    }

    return stats


def find_text_similarities(text1: str, text2: str) -> float:
    """计算两个文本的相似度（简单实现）"""
    # 提取关键词
    keywords1 = set(extract_keywords(text1, 20))
    keywords2 = set(extract_keywords(text2, 20))

    if not keywords1 and not keywords2:
        return 0.0

    # 计算Jaccard相似度
    intersection = len(keywords1.intersection(keywords2))
    union = len(keywords1.union(keywords2))

    return intersection / union if union > 0 else 0.0


def highlight_keywords(text: str, keywords: List[str]) -> str:
    """在文本中高亮关键词（用于Web显示）"""
    highlighted_text = text

    for keyword in keywords:
        # 使用HTML标签高亮
        pattern = re.compile(re.escape(keyword), re.IGNORECASE)
        highlighted_text = pattern.sub(
            f'<mark>{keyword}</mark>',
            highlighted_text
        )

    return highlighted_text
