#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
通用工具函数
"""

import os
import json
import yaml
import hashlib
import threading
from typing import Dict, Any, Optional


# 用户级锁字典
user_locks = {}


def get_user_lock(user_id: str) -> threading.RLock:
    """获取用户专属锁"""
    if user_id not in user_locks:
        user_locks[user_id] = threading.RLock()
    return user_locks[user_id]


def with_user_lock(func):
    """用户级锁装饰器"""
    def wrapper(user_id, *args, **kwargs):
        with get_user_lock(user_id):
            return func(user_id, *args, **kwargs)
    return wrapper


def load_json(file_path: str) -> Optional[Dict[str, Any]]:
    """加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"错误: 无法加载JSON文件 {file_path}: {e}")
        return None


def save_json(file_path: str, data: Dict[str, Any]) -> bool:
    """保存JSON文件"""
    try:
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"错误: 无法保存JSON文件 {file_path}: {e}")
        return False


def load_yaml(file_path: str) -> Optional[Dict[str, Any]]:
    """加载YAML文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except Exception as e:
        print(f"错误: 无法加载YAML文件 {file_path}: {e}")
        return None


def save_yaml(file_path: str, data: Dict[str, Any]) -> bool:
    """保存YAML文件"""
    try:
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        with open(file_path, 'w', encoding='utf-8') as f:
            yaml.dump(data, f, default_flow_style=False, allow_unicode=True)
        return True
    except Exception as e:
        print(f"错误: 无法保存YAML文件 {file_path}: {e}")
        return False


def calculate_text_hash(text: str) -> str:
    """计算文本的哈希值"""
    return hashlib.md5(text.encode('utf-8')).hexdigest()


def is_chinese(text: str) -> bool:
    """检测文本是否主要为中文"""
    if not text:
        return False
    
    chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
    return chinese_chars > len(text) * 0.5


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"


def safe_filename(filename: str) -> str:
    """生成安全的文件名"""
    # 移除或替换不安全的字符
    unsafe_chars = '<>:"/\\|?*'
    for char in unsafe_chars:
        filename = filename.replace(char, '_')
    
    # 限制长度
    if len(filename) > 200:
        name, ext = os.path.splitext(filename)
        filename = name[:200-len(ext)] + ext
    
    return filename
