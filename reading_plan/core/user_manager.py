#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
用户状态管理模块
"""

import os
import time
import json
from typing import Dict, Any, Optional, List

from reading_plan.utils.helpers import (
    load_json, save_json, with_user_lock, calculate_text_hash
)


def get_user_state_path(user_id: str, config: Dict[str, Any]) -> str:
    """获取用户状态文件路径"""
    logs_dir = os.path.join(config["system"]["data_dir"], "logs")
    return os.path.join(logs_dir, f"{user_id}_state.json")


@with_user_lock
def load_user_state(user_id: str, config: Dict[str, Any]) -> Dict[str, Any]:
    """加载用户状态"""
    state_path = get_user_state_path(user_id, config)
    
    # 如果状态文件存在，加载它
    if os.path.exists(state_path):
        state = load_json(state_path)
        if state:
            return state
    
    # 否则创建默认状态
    return create_default_user_state(user_id, config)


def create_default_user_state(user_id: str, config: Dict[str, Any]) -> Dict[str, Any]:
    """创建默认用户状态"""
    default_state = {
        "username": user_id,
        "createdAt": time.time(),
        "lastActivity": None,
        "interface": None,  # 'cli' 或 'web'
        "cognitiveRadar": {domain: 0 for domain in config["cognitive_domains"]},
        "currentState": {
            "currentBook": None,
            "readingState": "not_started",  # not_started, in_progress, finished
            "progressPointer": {
                "currentMode": config["reading"]["default_mode"],
                "lastTextBlockHash": None,
                "lastPosition": 0
            }
        },
        "readHistory": []
    }
    
    # 保存默认状态
    state_path = get_user_state_path(user_id, config)
    save_json(state_path, default_state)
    
    return default_state


@with_user_lock
def save_user_state(user_id: str, state: Dict[str, Any], config: Dict[str, Any]) -> bool:
    """保存用户状态"""
    # 更新最后活动时间
    state["lastActivity"] = time.time()
    
    # 保存状态
    state_path = get_user_state_path(user_id, config)
    return save_json(state_path, state)


@with_user_lock
def update_reading_progress(
    user_id: str,
    book_id: str,
    text_block: str,
    block_position: int,
    reading_mode: str,
    config: Dict[str, Any]
) -> bool:
    """更新阅读进度"""
    # 加载用户状态
    state = load_user_state(user_id, config)
    
    # 计算文本块哈希
    text_hash = calculate_text_hash(text_block)
    
    # 更新进度
    state["currentState"]["currentBook"] = book_id
    state["currentState"]["readingState"] = "in_progress"
    state["currentState"]["progressPointer"]["currentMode"] = reading_mode
    state["currentState"]["progressPointer"]["lastTextBlockHash"] = text_hash
    state["currentState"]["progressPointer"]["lastPosition"] = block_position
    
    # 保存状态
    return save_user_state(user_id, state, config)


@with_user_lock
def mark_book_as_finished(
    user_id: str,
    book_id: str,
    book_data: Dict[str, Any],
    config: Dict[str, Any]
) -> bool:
    """标记书籍为已完成并更新认知雷达图"""
    # 加载用户状态
    state = load_user_state(user_id, config)
    
    # 添加到阅读历史
    read_history_entry = {
        "bookId": book_id,
        "finishedAt": time.time(),
        "rating": None,  # 用户可以后续添加评分
        "notes": []  # 用户笔记
    }
    
    state["readHistory"].append(read_history_entry)
    
    # 更新认知雷达图
    if "cognitiveDomains" in book_data:
        for domain, value in book_data["cognitiveDomains"].items():
            if domain in state["cognitiveRadar"]:
                # 应用衰减因子
                current_value = state["cognitiveRadar"][domain]
                decay_factor = 1.0 / (1.0 + (current_value / 100.0))
                
                # 更新值
                increment = value * decay_factor
                state["cognitiveRadar"][domain] = current_value + increment
    
    # 重置当前阅读状态
    state["currentState"]["currentBook"] = None
    state["currentState"]["readingState"] = "not_started"
    state["currentState"]["progressPointer"]["lastTextBlockHash"] = None
    state["currentState"]["progressPointer"]["lastPosition"] = 0
    
    # 保存状态
    return save_user_state(user_id, state, config)


@with_user_lock
def backup_user_data(user_id: str, config: Dict[str, Any]) -> Optional[str]:
    """备份用户数据"""
    # 加载用户状态
    state = load_user_state(user_id, config)
    
    # 备份目录
    backup_dir = config["system"]["backup_dir"]
    os.makedirs(backup_dir, exist_ok=True)
    
    # 备份文件路径
    backup_path = os.path.join(backup_dir, f"{user_id}_backup.json")
    
    # 保存备份
    if save_json(backup_path, state):
        print(f"用户 {user_id} 数据已备份至 {backup_path}")
        return backup_path
    
    return None


def list_available_users(config: Dict[str, Any]) -> List[str]:
    """列出所有可用用户"""
    logs_dir = os.path.join(config["system"]["data_dir"], "logs")
    os.makedirs(logs_dir, exist_ok=True)
    
    users = []
    for filename in os.listdir(logs_dir):
        if filename.endswith("_state.json"):
            user_id = filename.replace("_state.json", "")
            users.append(user_id)
    
    return users
