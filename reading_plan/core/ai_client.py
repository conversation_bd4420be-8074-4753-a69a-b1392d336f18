#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI客户端模块 - 负责与本地LM Studio通信
"""

import time
import requests
from typing import Dict, Any, Optional, List


def call_local_ai_with_retry(
    prompt: str,
    api_url: str = "http://192.168.3.70:1234",
    model: str = "qwen3-30b-a3b",
    max_retries: int = 5,
    timeout: int = 20,
    temperature: float = 0.1
) -> Optional[str]:
    """带重试机制的AI调用"""
    for attempt in range(max_retries):
        try:
            response = requests.post(
                f"{api_url}/v1/chat/completions",
                json={
                    "model": model,
                    "messages": [{"role": "user", "content": prompt}],
                    "stream": False,
                    "temperature": temperature
                },
                timeout=timeout
            )
            
            if response.status_code == 200:
                return response.json()["choices"][0]["message"]["content"]
            
            print(f"AI调用返回错误状态码: {response.status_code}")
            
        except (requests.Timeout, requests.ConnectionError) as e:
            print(f"AI调用失败 (尝试 {attempt+1}/{max_retries}): {str(e)}")
        
        # 短暂延迟后重试
        time.sleep(2)
    
    # 所有重试都失败，返回None触发降级
    print("AI调用失败，已达到最大重试次数")
    return None


def is_ai_available(
    api_url: str = "http://192.168.3.70:1234",
    timeout: int = 5
) -> bool:
    """检测AI服务是否可用"""
    try:
        response = requests.get(f"{api_url}/v1/models", timeout=timeout)
        return response.status_code == 200
    except:
        return False


def translate_text(
    text: str,
    target_language: str = 'zh',
    api_url: str = "http://192.168.3.70:1234",
    model: str = "qwen3-30b-a3b"
) -> str:
    """使用LLM翻译文本"""
    from reading_plan.utils.helpers import is_chinese
    
    # 已经是中文，无需翻译
    if is_chinese(text):
        return text
        
    prompt = f"""请将以下文本翻译成中文，保持原文的意思、风格和格式:

{text}

仅返回翻译结果，不要添加任何解释。"""
    
    translated = call_local_ai_with_retry(
        prompt=prompt,
        api_url=api_url,
        model=model
    )
    
    if translated:
        return translated
    
    # AI不可用时返回原文
    return text + "\n[翻译服务不可用]"


def generate_ai_summary(
    text: str,
    api_url: str = "http://192.168.3.70:1234",
    model: str = "qwen3-30b-a3b"
) -> Optional[str]:
    """生成文本摘要"""
    prompt = f"""请对以下文本进行精炼转述，保持原文的核心意思和风格，但使用更简洁、更易于理解的语言:

{text}

请直接给出转述结果，不要添加任何解释或前缀。"""
    
    return call_local_ai_with_retry(
        prompt=prompt,
        api_url=api_url,
        model=model
    )
