#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
阅读引擎模块 - 核心阅读逻辑和三级降级机制
"""

import os
import time
from typing import Dict, Any, List, Optional, Tuple

from reading_plan.core.document_parser import parse_document, validate_document
from reading_plan.core.ai_client import (
    call_local_ai_with_retry, is_ai_available, generate_ai_summary, translate_text
)
from reading_plan.core.user_manager import (
    load_user_state, save_user_state, update_reading_progress
)
from reading_plan.utils.helpers import load_json, save_json, calculate_text_hash
from reading_plan.utils.text_processor import smart_split_text, summarize_text_stats


class ReadingEngine:
    """阅读引擎类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.ai_config = config["ai"]
        self.reading_config = config["reading"]
        
    def load_book(self, book_path: str) -> Dict[str, Any]:
        """加载书籍"""
        # 验证文档
        is_valid, message = validate_document(book_path)
        if not is_valid:
            raise ValueError(f"无法加载书籍: {message}")
        
        # 解析文档
        book_data = parse_document(book_path, self.config)
        
        # 生成书籍ID
        book_id = os.path.splitext(os.path.basename(book_path))[0]
        book_data['book_id'] = book_id
        
        return book_data
    
    def get_text_block_with_fallback(
        self, 
        book_data: Dict[str, Any], 
        block_index: int, 
        user_id: str,
        requested_mode: str = None
    ) -> Dict[str, Any]:
        """获取文本块，带三级降级机制"""
        
        # 获取文本块
        text_blocks = book_data.get('text_blocks', [])
        if block_index >= len(text_blocks):
            return {
                'content': '',
                'mode': 'end_of_book',
                'fallback_level': 0,
                'message': '已到达书籍末尾'
            }
        
        original_block = text_blocks[block_index]
        original_text = original_block['text']
        
        # 获取用户状态确定阅读模式
        user_state = load_user_state(user_id, self.config)
        current_mode = requested_mode or user_state.get('currentState', {}).get('progressPointer', {}).get('currentMode', 'ai_summary')
        
        # 如果请求原文模式，直接返回
        if current_mode == 'original_text':
            return {
                'content': original_text,
                'mode': 'original_text',
                'fallback_level': 0,
                'block_info': original_block,
                'message': '原文模式'
            }
        
        # 尝试AI增强模式
        ai_available = is_ai_available(self.ai_config["api_url"])
        
        if ai_available and current_mode == 'ai_summary':
            # 第一级：尝试生成AI摘要
            summary = self._generate_summary_with_cache(original_text, book_data['book_id'], block_index)
            
            if summary:
                return {
                    'content': summary,
                    'mode': 'ai_summary',
                    'fallback_level': 0,
                    'block_info': original_block,
                    'message': 'AI精炼转述'
                }
        
        # 第二级：尝试使用缓存的AI摘要
        cached_summary = self._get_cached_summary(book_data['book_id'], block_index)
        if cached_summary:
            return {
                'content': cached_summary,
                'mode': 'ai_summary',
                'fallback_level': 1,
                'block_info': original_block,
                'message': 'AI精炼转述（缓存）'
            }
        
        # 第三级：回退到原文模式
        return {
            'content': original_text,
            'mode': 'original_text',
            'fallback_level': 2,
            'block_info': original_block,
            'message': '原文模式（AI服务不可用）'
        }
    
    def _generate_summary_with_cache(self, text: str, book_id: str, block_index: int) -> Optional[str]:
        """生成摘要并缓存"""
        # 检查缓存
        cache_path = self._get_summary_cache_path(book_id, block_index)
        
        if os.path.exists(cache_path):
            cached_data = load_json(cache_path)
            if cached_data and cached_data.get('text_hash') == calculate_text_hash(text):
                return cached_data.get('summary')
        
        # 生成新摘要
        summary = generate_ai_summary(
            text,
            api_url=self.ai_config["api_url"],
            model=self.ai_config["model"]
        )
        
        if summary:
            # 保存到缓存
            cache_data = {
                'summary': summary,
                'text_hash': calculate_text_hash(text),
                'generated_at': time.time()
            }
            
            os.makedirs(os.path.dirname(cache_path), exist_ok=True)
            save_json(cache_path, cache_data)
        
        return summary
    
    def _get_cached_summary(self, book_id: str, block_index: int) -> Optional[str]:
        """获取缓存的摘要"""
        cache_path = self._get_summary_cache_path(book_id, block_index)
        
        if os.path.exists(cache_path):
            cached_data = load_json(cache_path)
            if cached_data:
                return cached_data.get('summary')
        
        return None
    
    def _get_summary_cache_path(self, book_id: str, block_index: int) -> str:
        """获取摘要缓存路径"""
        cache_dir = os.path.join(self.config["system"]["cache_dir"], "summaries", book_id)
        return os.path.join(cache_dir, f"block_{block_index}.json")
    
    def switch_reading_mode(self, user_id: str, new_mode: str) -> bool:
        """切换阅读模式"""
        if new_mode not in ['ai_summary', 'original_text']:
            return False
        
        # 更新用户状态
        user_state = load_user_state(user_id, self.config)
        user_state['currentState']['progressPointer']['currentMode'] = new_mode
        
        return save_user_state(user_id, user_state, self.config)
    
    def navigate_to_block(
        self, 
        user_id: str, 
        book_data: Dict[str, Any], 
        block_index: int,
        reading_mode: str = None
    ) -> Dict[str, Any]:
        """导航到指定文本块"""
        
        # 获取文本块
        result = self.get_text_block_with_fallback(book_data, block_index, user_id, reading_mode)
        
        if result['mode'] != 'end_of_book':
            # 更新阅读进度
            update_reading_progress(
                user_id=user_id,
                book_id=book_data['book_id'],
                text_block=result['content'],
                block_position=block_index,
                reading_mode=result['mode'],
                config=self.config
            )
        
        return result
    
    def get_reading_menu(self, user_id: str, book_data: Dict[str, Any], current_block_index: int) -> List[Dict[str, str]]:
        """生成阅读菜单选项"""
        user_state = load_user_state(user_id, self.config)
        current_mode = user_state.get('currentState', {}).get('progressPointer', {}).get('currentMode', 'ai_summary')
        
        menu_options = []
        
        # 继续阅读选项
        total_blocks = len(book_data.get('text_blocks', []))
        if current_block_index < total_blocks - 1:
            menu_options.append({
                'key': 'next',
                'label': f'继续阅读 [{current_mode}模式]',
                'description': '阅读下一段内容'
            })
        
        # 模式切换选项
        if current_mode == 'ai_summary':
            menu_options.append({
                'key': 'switch_original',
                'label': '查看【原文】',
                'description': '切换到原文阅读模式'
            })
        else:
            menu_options.append({
                'key': 'switch_summary',
                'label': '返回【摘要模式】',
                'description': '切换到AI精炼转述模式'
            })
        
        # 导航选项
        if current_block_index > 0:
            menu_options.append({
                'key': 'previous',
                'label': '返回上一段',
                'description': '阅读上一段内容'
            })
        
        # 进度选项
        menu_options.append({
            'key': 'progress',
            'label': '查看进度',
            'description': f'当前进度: {current_block_index + 1}/{total_blocks}'
        })
        
        # 书籍信息
        menu_options.append({
            'key': 'book_info',
            'label': '书籍信息',
            'description': '查看书籍详细信息'
        })
        
        # 退出选项
        menu_options.append({
            'key': 'exit',
            'label': '退出阅读',
            'description': '保存进度并退出'
        })
        
        return menu_options
    
    def get_book_info(self, book_data: Dict[str, Any]) -> Dict[str, Any]:
        """获取书籍信息"""
        text_blocks = book_data.get('text_blocks', [])
        total_text = ' '.join([block['text'] for block in text_blocks])
        
        stats = summarize_text_stats(total_text)
        
        info = {
            'title': book_data.get('title', '未知标题'),
            'author': book_data.get('author', '未知作者'),
            'format': book_data.get('file_format', ''),
            'file_size': book_data.get('file_size', 0),
            'total_blocks': len(text_blocks),
            'statistics': stats,
            'metadata': book_data.get('metadata', {})
        }
        
        return info
    
    def translate_if_needed(self, text: str) -> str:
        """如果需要，翻译文本为中文"""
        from reading_plan.utils.helpers import is_chinese
        
        if not is_chinese(text):
            # 检查AI是否可用
            if is_ai_available(self.ai_config["api_url"]):
                translated = translate_text(
                    text,
                    api_url=self.ai_config["api_url"],
                    model=self.ai_config["model"]
                )
                return translated
            else:
                return text + "\n[翻译服务不可用]"
        
        return text
    
    def get_reading_progress(self, user_id: str, book_data: Dict[str, Any]) -> Dict[str, Any]:
        """获取阅读进度信息"""
        user_state = load_user_state(user_id, self.config)
        current_state = user_state.get('currentState', {})
        
        total_blocks = len(book_data.get('text_blocks', []))
        current_position = current_state.get('progressPointer', {}).get('lastPosition', 0)
        
        progress_percentage = (current_position / total_blocks * 100) if total_blocks > 0 else 0
        
        return {
            'current_position': current_position,
            'total_blocks': total_blocks,
            'progress_percentage': round(progress_percentage, 1),
            'reading_mode': current_state.get('progressPointer', {}).get('currentMode', 'ai_summary'),
            'book_title': book_data.get('title', '未知标题')
        }
