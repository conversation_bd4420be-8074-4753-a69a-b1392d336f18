#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
命令行界面模块
"""

import os
import sys
from typing import Dict, Any, List, Optional

from reading_plan.core.user_manager import (
    load_user_state, save_user_state, backup_user_data, list_available_users
)
from reading_plan.core.ai_client import is_ai_available


def print_banner():
    """打印程序横幅"""
    print("=" * 60)
    print("           柔性阅读计划 v6.0")
    print("        个性化阅读平台 - 命令行界面")
    print("=" * 60)
    print()


def authenticate_user(config: Dict[str, Any]) -> Optional[str]:
    """用户身份认证"""
    print("您好！为了给您提供最合适的帮助，请问现在是哪位用户在与我交流？")
    print()
    
    # 显示可用用户
    available_users = list_available_users(config)
    default_users = ["老汉", "啾啾", "妙妙", "测试"]
    
    all_users = list(set(default_users + available_users))
    
    for i, user in enumerate(all_users, 1):
        print(f"{i}. {user}")
    
    print(f"{len(all_users) + 1}. 创建新用户")
    print("0. 退出")
    print()
    
    while True:
        try:
            choice = input("请选择 (输入数字): ").strip()
            
            if choice == "0":
                return None
            
            choice_num = int(choice)
            
            if 1 <= choice_num <= len(all_users):
                return all_users[choice_num - 1]
            elif choice_num == len(all_users) + 1:
                # 创建新用户
                new_user = input("请输入新用户名: ").strip()
                if new_user:
                    return new_user
                else:
                    print("用户名不能为空，请重新选择。")
            else:
                print("无效选择，请重新输入。")
                
        except ValueError:
            print("请输入有效的数字。")
        except KeyboardInterrupt:
            print("\n\n再见！")
            return None


def admin_mode(config: Dict[str, Any]) -> None:
    """老汉管理模式"""
    print("\n" + "=" * 40)
    print("         管理员模式")
    print("=" * 40)
    
    while True:
        print("\n管理选项:")
        print("1. 查看所有用户状态")
        print("2. 查看系统配置")
        print("3. 检查AI服务状态")
        print("4. 数据备份管理")
        print("0. 返回主菜单")
        
        choice = input("\n请选择: ").strip()
        
        if choice == "0":
            break
        elif choice == "1":
            show_all_users_status(config)
        elif choice == "2":
            show_system_config(config)
        elif choice == "3":
            check_ai_status(config)
        elif choice == "4":
            backup_management(config)
        else:
            print("无效选择，请重新输入。")


def show_all_users_status(config: Dict[str, Any]) -> None:
    """显示所有用户状态"""
    print("\n" + "-" * 40)
    print("用户状态概览")
    print("-" * 40)
    
    users = list_available_users(config)
    
    if not users:
        print("暂无用户数据。")
        return
    
    for user_id in users:
        state = load_user_state(user_id, config)
        current_book = state["currentState"]["currentBook"]
        reading_state = state["currentState"]["readingState"]
        
        print(f"\n用户: {user_id}")
        print(f"  当前书籍: {current_book or '无'}")
        print(f"  阅读状态: {reading_state}")
        print(f"  已读书籍: {len(state.get('readHistory', []))}")


def show_system_config(config: Dict[str, Any]) -> None:
    """显示系统配置"""
    print("\n" + "-" * 40)
    print("系统配置")
    print("-" * 40)
    
    print(f"版本: {config['system']['version']}")
    print(f"数据目录: {config['system']['data_dir']}")
    print(f"AI服务地址: {config['ai']['api_url']}")
    print(f"AI模型: {config['ai']['model']}")
    print(f"默认阅读模式: {config['reading']['default_mode']}")


def check_ai_status(config: Dict[str, Any]) -> None:
    """检查AI服务状态"""
    print("\n" + "-" * 40)
    print("AI服务状态检查")
    print("-" * 40)
    
    api_url = config["ai"]["api_url"]
    print(f"正在检查 {api_url} ...")
    
    if is_ai_available(api_url):
        print("✓ AI服务正常运行")
    else:
        print("✗ AI服务不可用")
        print("  请检查LM Studio是否正在运行")


def backup_management(config: Dict[str, Any]) -> None:
    """备份管理"""
    print("\n" + "-" * 40)
    print("数据备份管理")
    print("-" * 40)
    
    users = list_available_users(config)
    
    if not users:
        print("暂无用户数据需要备份。")
        return
    
    print("选择要备份的用户:")
    for i, user in enumerate(users, 1):
        print(f"{i}. {user}")
    print("0. 备份所有用户")
    
    choice = input("\n请选择: ").strip()
    
    try:
        if choice == "0":
            # 备份所有用户
            for user in users:
                backup_user_data(user, config)
        else:
            choice_num = int(choice)
            if 1 <= choice_num <= len(users):
                user = users[choice_num - 1]
                backup_user_data(user, config)
            else:
                print("无效选择。")
    except ValueError:
        print("请输入有效的数字。")


def reading_mode(user_id: str, config: Dict[str, Any]) -> None:
    """用户阅读模式"""
    print(f"\n欢迎回来，{user_id}！")
    
    # 加载用户状态
    state = load_user_state(user_id, config)
    
    # 设置界面类型
    state["interface"] = "cli"
    save_user_state(user_id, state, config)
    
    # 检查是否有正在阅读的书籍
    current_book = state["currentState"]["currentBook"]
    reading_state = state["currentState"]["readingState"]
    
    if current_book and reading_state == "in_progress":
        print(f"检测到您正在阅读《{current_book}》，是否继续？")
        print("1. 继续阅读")
        print("2. 选择新书")
        print("0. 注销")
        
        choice = input("\n请选择: ").strip()
        
        if choice == "1":
            # TODO: 实现断点续读
            print("断点续读功能开发中...")
        elif choice == "2":
            # TODO: 实现书籍选择
            print("书籍选择功能开发中...")
        else:
            logout_user(user_id, config)
            return
    else:
        # TODO: 实现新用户流程
        print("新用户流程开发中...")
    
    # 用户注销时备份数据
    logout_user(user_id, config)


def logout_user(user_id: str, config: Dict[str, Any]) -> None:
    """用户注销"""
    print(f"\n{user_id}，感谢您的使用！")
    
    # 备份用户数据
    backup_user_data(user_id, config)
    
    print("数据已保存，再见！")


def start_cli_interface(config: Dict[str, Any]) -> None:
    """启动命令行界面"""
    print_banner()
    
    try:
        while True:
            # 用户身份认证
            user_id = authenticate_user(config)
            
            if user_id is None:
                print("再见！")
                break
            
            # 根据用户类型进入不同模式
            if user_id == "老汉":
                admin_mode(config)
            else:
                reading_mode(user_id, config)
                
    except KeyboardInterrupt:
        print("\n\n程序已退出，再见！")
    except Exception as e:
        print(f"\n发生错误: {e}")
        print("程序已退出。")
