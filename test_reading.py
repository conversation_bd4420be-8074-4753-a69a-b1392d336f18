#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的阅读测试脚本
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_reading():
    """测试阅读功能"""
    print("=" * 60)
    print("柔性阅读计划 - 阅读测试")
    print("=" * 60)
    
    try:
        # 导入模块
        from reading_plan.config.settings import load_config
        from reading_plan.core.reading_engine import ReadingEngine
        from reading_plan.core.user_manager import load_user_state
        
        print("✓ 模块导入成功")
        
        # 加载配置
        config = load_config("config.yaml")
        print("✓ 配置加载成功")
        
        # 初始化阅读引擎
        reading_engine = ReadingEngine(config)
        print("✓ 阅读引擎初始化成功")
        
        # 查找书籍
        books_dirs = ["1_书库_Library", "data/books"]
        available_books = []
        
        for books_dir in books_dirs:
            if os.path.exists(books_dir):
                for filename in os.listdir(books_dir):
                    file_path = os.path.join(books_dir, filename)
                    if os.path.isfile(file_path):
                        file_ext = os.path.splitext(filename)[1].lower()
                        if file_ext in ['.epub', '.txt', '.md', '.docx']:
                            available_books.append(file_path)
        
        print(f"✓ 找到 {len(available_books)} 本书籍")
        
        if available_books:
            # 测试加载第一本书
            test_book = available_books[0]
            print(f"测试书籍: {os.path.basename(test_book)}")
            
            try:
                book_data = reading_engine.load_book(test_book)
                print(f"✓ 书籍加载成功")
                print(f"  标题: {book_data.get('title', '未知')}")
                print(f"  作者: {book_data.get('author', '未知')}")
                print(f"  文本块数: {len(book_data.get('text_blocks', []))}")
                
                # 测试获取第一个文本块
                if book_data.get('text_blocks'):
                    result = reading_engine.get_text_block_with_fallback(
                        book_data, 0, "测试"
                    )
                    print(f"✓ 文本块获取成功")
                    print(f"  模式: {result['mode']}")
                    print(f"  降级级别: {result['fallback_level']}")
                    print(f"  内容长度: {len(result['content'])} 字符")
                    
                    # 显示前200字符
                    preview = result['content'][:200] + "..." if len(result['content']) > 200 else result['content']
                    print(f"  内容预览: {preview}")
                
            except Exception as e:
                print(f"✗ 书籍加载失败: {str(e)}")
        
        print("\n" + "=" * 60)
        print("测试完成！系统运行正常。")
        print("=" * 60)
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_reading()
