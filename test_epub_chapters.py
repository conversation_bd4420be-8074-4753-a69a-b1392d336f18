#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试EPUB章节分块功能
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def test_epub_chapter_parsing():
    """测试EPUB章节解析"""
    from core.document_parser import parse_document

    print("=" * 60)
    print("测试EPUB章节分块功能")
    print("=" * 60)

    # 测试夏洛的网
    epub_path = "1_书库_Library/夏洛的网.epub"

    if not os.path.exists(epub_path):
        print(f"❌ 未找到测试文件: {epub_path}")
        return

    try:
        from config.settings import load_config
        config = load_config()

        print(f"📖 解析文件: {epub_path}")
        book_data = parse_document(epub_path, config)

        print(f"✅ 解析成功!")
        print(f"📚 书名: {book_data['title']}")
        print(f"👤 作者: {book_data['author']}")
        print(f"📄 总章节数: {book_data.get('total_chapters', 0)}")
        print(f"🧩 文本块数: {len(book_data.get('text_blocks', []))}")

        # 显示前几个文本块的章节信息
        text_blocks = book_data.get('text_blocks', [])
        print(f"\n📋 前5个文本块的章节信息:")

        for i, block in enumerate(text_blocks[:5]):
            chapter_info = block.get('chapter_info')
            if chapter_info:
                print(f"  块 {i+1}: {chapter_info.get('chapter_title', '未知章节')}")
                if not chapter_info.get('is_complete_chapter', True):
                    print(f"    └─ {chapter_info.get('part_title', '未知部分')}")
                print(f"    字符数: {block.get('char_count', 0)}")
            else:
                print(f"  块 {i+1}: 无章节信息 (字符数: {block.get('char_count', 0)})")

        # 显示章节分布统计
        print(f"\n📊 章节分布统计:")
        chapter_stats = {}
        for block in text_blocks:
            chapter_info = block.get('chapter_info')
            if chapter_info:
                chapter_num = chapter_info.get('chapter_number', 0)
                if chapter_num not in chapter_stats:
                    chapter_stats[chapter_num] = {
                        'title': chapter_info.get('chapter_title', ''),
                        'blocks': 0,
                        'total_chars': 0
                    }
                chapter_stats[chapter_num]['blocks'] += 1
                chapter_stats[chapter_num]['total_chars'] += block.get(
                    'char_count', 0)

        for chapter_num, stats in sorted(chapter_stats.items()):
            print(f"  第{chapter_num}章: {stats['title']}")
            print(f"    文本块数: {stats['blocks']}, 总字符数: {stats['total_chars']}")

        print(f"\n✅ EPUB章节分块测试完成!")

    except Exception as e:
        print(f"❌ 解析失败: {e}")
        import traceback
        traceback.print_exc()


def test_reading_with_chapters():
    """测试带章节信息的阅读功能"""
    from core.reading_engine import ReadingEngine
    from config.settings import load_config

    print(f"\n" + "=" * 60)
    print("测试带章节信息的阅读功能")
    print("=" * 60)

    try:
        config = load_config()
        reading_engine = ReadingEngine(config)

        # 加载书籍
        epub_path = "1_书库_Library/夏洛的网.epub"
        book_data = reading_engine.load_book(epub_path)

        print(f"📖 加载书籍: {book_data['title']}")

        # 测试前3个文本块
        user_id = "测试"
        for i in range(min(3, len(book_data['text_blocks']))):
            print(f"\n--- 文本块 {i+1} ---")

            result = reading_engine.navigate_to_block(
                user_id, book_data, i, 'original_text')

            # 显示章节信息
            block_info = result.get('block_info', {})
            chapter_info = block_info.get('chapter_info')

            if chapter_info:
                print(f"📚 章节: {chapter_info.get('chapter_title', '未知')}")
                if not chapter_info.get('is_complete_chapter', True):
                    print(f"📄 部分: {chapter_info.get('part_title', '未知')}")

            print(f"📝 模式: {result['message']}")
            print(f"📊 字符数: {len(result['content'])}")
            print(f"📄 内容预览: {result['content'][:100]}...")

        print(f"\n✅ 阅读功能测试完成!")

    except Exception as e:
        print(f"❌ 阅读测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_epub_chapter_parsing()
    test_reading_with_chapters()
