#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的阅读体验测试
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_reading_experience():
    """测试阅读体验"""
    print("=" * 60)
    print("柔性阅读计划 - 阅读体验测试")
    print("=" * 60)
    
    try:
        from reading_plan.config.settings import load_config
        from reading_plan.core.reading_engine import ReadingEngine
        from reading_plan.core.user_manager import load_user_state, save_user_state
        
        # 加载配置
        config = load_config("config.yaml")
        reading_engine = ReadingEngine(config)
        
        # 查找书籍
        books_dir = "1_书库_Library"
        available_books = []
        
        if os.path.exists(books_dir):
            for filename in os.listdir(books_dir):
                if filename.endswith('.epub'):
                    available_books.append(os.path.join(books_dir, filename))
        
        if not available_books:
            print("未找到EPUB书籍")
            return
        
        # 选择一本小书进行测试
        test_book = None
        for book in available_books:
            if "夏洛的网" in book:
                test_book = book
                break
        
        if not test_book:
            test_book = available_books[0]
        
        print(f"测试书籍: {os.path.basename(test_book)}")
        
        # 加载书籍
        book_data = reading_engine.load_book(test_book)
        print(f"书籍加载成功！")
        print(f"标题: {book_data.get('title', '未知')}")
        print(f"作者: {book_data.get('author', '未知')}")
        print(f"文本块数: {len(book_data.get('text_blocks', []))}")
        
        # 设置用户为原文模式（避免AI调用）
        user_id = "测试"
        user_state = load_user_state(user_id, config)
        user_state['currentState']['progressPointer']['currentMode'] = 'original_text'
        save_user_state(user_id, user_state, config)
        
        print("\n" + "=" * 60)
        print("开始阅读体验测试（原文模式）")
        print("=" * 60)
        
        # 测试前3个文本块
        for i in range(min(3, len(book_data.get('text_blocks', [])))):
            print(f"\n📖 文本块 {i+1}:")
            print("-" * 40)
            
            result = reading_engine.navigate_to_block(user_id, book_data, i, 'original_text')
            
            content = result['content']
            preview = content[:300] + "..." if len(content) > 300 else content
            
            print(f"模式: {result['message']}")
            print(f"字符数: {len(content)}")
            print(f"内容预览:\n{preview}")
            print("-" * 40)
        
        # 测试阅读菜单
        print(f"\n📋 阅读菜单选项:")
        menu_options = reading_engine.get_reading_menu(user_id, book_data, 0)
        for i, option in enumerate(menu_options, 1):
            print(f"{i}. {option['label']} - {option['description']}")
        
        # 测试阅读进度
        print(f"\n📊 阅读进度:")
        progress = reading_engine.get_reading_progress(user_id, book_data)
        print(f"当前位置: {progress['current_position'] + 1}/{progress['total_blocks']}")
        print(f"完成度: {progress['progress_percentage']}%")
        print(f"阅读模式: {progress['reading_mode']}")
        
        print("\n" + "=" * 60)
        print("✅ 阅读体验测试完成！系统功能正常。")
        print("=" * 60)
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_reading_experience()
