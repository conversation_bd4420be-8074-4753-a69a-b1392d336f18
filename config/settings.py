#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
系统配置管理
"""

import os
import yaml
import json
from typing import Dict, Any, Optional


# 默认配置
DEFAULT_CONFIG = {
    "system": {
        "version": "6.0.0",
        "data_dir": "data",
        "cache_dir": "data/cache",
        "backup_dir": "data/backups"
    },
    "ai": {
        "api_url": "http://192.168.3.70:1234",
        "model": "qwen3-30b-a3b",
        "timeout": 20,
        "max_retries": 5,
        "temperature": 0.1
    },
    "reading": {
        "default_block_size": 2000,
        "supported_formats": [".pdf", ".epub", ".docx", ".txt"],
        "default_mode": "ai_summary"
    },
    "cognitive_domains": [
        "科普与自然",
        "逻辑与思维",
        "历史与人文",
        "社会与情感",
        "文学与故事",
        "艺术与审美"
    ]
}


def load_config(config_path: Optional[str] = None) -> Dict[str, Any]:
    """加载系统配置"""
    config = DEFAULT_CONFIG.copy()

    # 如果指定了配置文件路径，尝试加载
    if config_path and os.path.exists(config_path):
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.endswith('.yaml') or config_path.endswith('.yml'):
                    user_config = yaml.safe_load(f)
                else:
                    user_config = json.load(f)

                # 合并配置
                config.update(user_config)
        except Exception as e:
            print(f"警告: 无法加载配置文件 {config_path}: {e}")

    # 确保数据目录存在
    ensure_directories(config)

    return config


def ensure_directories(config: Dict[str, Any]) -> None:
    """确保必要的目录存在"""
    directories = [
        config["system"]["data_dir"],
        config["system"]["cache_dir"],
        config["system"]["backup_dir"],
        os.path.join(config["system"]["data_dir"], "books"),
        os.path.join(config["system"]["data_dir"], "analysis"),
        os.path.join(config["system"]["data_dir"], "logs"),
    ]

    for directory in directories:
        os.makedirs(directory, exist_ok=True)


def save_config(config: Dict[str, Any], config_path: str) -> None:
    """保存配置到文件"""
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            if config_path.endswith('.yaml') or config_path.endswith('.yml'):
                yaml.dump(config, f, default_flow_style=False,
                          allow_unicode=True)
            else:
                json.dump(config, f, indent=2, ensure_ascii=False)
    except Exception as e:
        print(f"错误: 无法保存配置文件 {config_path}: {e}")
