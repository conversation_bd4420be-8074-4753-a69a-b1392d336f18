#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Flask Web应用
"""

from flask import Flask, render_template, request, jsonify, session, redirect
from typing import Dict, Any


def create_app(config: Dict[str, Any]) -> Flask:
    """创建Flask应用"""
    app = Flask(__name__)
    app.secret_key = "reading_plan_secret_key_2024"  # 生产环境应使用随机密钥

    # 存储配置
    app.config['READING_PLAN_CONFIG'] = config

    @app.route('/')
    def index():
        """主页"""
        return render_template('index.html')

    @app.route('/login', methods=['GET', 'POST'])
    def login():
        """用户登录"""
        if request.method == 'POST':
            user_id = request.form.get('user_id')
            if user_id:
                session['user_id'] = user_id
                session['interface'] = 'web'
                return jsonify({'success': True, 'redirect': '/reading'})
            return jsonify({'success': False, 'message': '请输入用户名'})

        return render_template('login.html')

    @app.route('/reading')
    def reading():
        """阅读界面"""
        user_id = session.get('user_id')
        if not user_id:
            return redirect('/login')

        # 初始化阅读引擎
        from core.reading_engine import ReadingEngine
        from core.user_manager import load_user_state

        reading_engine = ReadingEngine(config)
        user_state = load_user_state(user_id, config)

        # 设置Web界面
        user_state['interface'] = 'web'
        from core.user_manager import save_user_state
        save_user_state(user_id, user_state, config)

        # 检查是否有正在阅读的书籍
        current_book = user_state["currentState"]["currentBook"]
        reading_state = user_state["currentState"]["readingState"]

        context = {
            'user_id': user_id,
            'current_book': current_book,
            'reading_state': reading_state,
            'has_current_book': current_book and reading_state == "in_progress"
        }

        return render_template('reading.html', **context)

    @app.route('/api/books')
    def api_books():
        """获取可用书籍列表"""
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'error': '未登录'}), 401

        # 查找可用书籍
        import os
        from utils.helpers import format_file_size

        books_dirs = [
            os.path.join(config["system"]["data_dir"], "books"),
            "1_书库_Library",
            "data/books"
        ]

        available_books = []
        for books_dir in books_dirs:
            if os.path.exists(books_dir):
                for filename in os.listdir(books_dir):
                    file_path = os.path.join(books_dir, filename)
                    if os.path.isfile(file_path):
                        file_ext = os.path.splitext(filename)[1].lower()
                        if file_ext in config["reading"]["supported_formats"]:
                            available_books.append({
                                'filename': filename,
                                'path': file_path,
                                'size': format_file_size(os.path.getsize(file_path)),
                                'format': file_ext
                            })
                break

        return jsonify({'books': available_books})

    @app.route('/api/start_book', methods=['POST'])
    def api_start_book():
        """开始阅读书籍"""
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'error': '未登录'}), 401

        data = request.get_json()
        book_path = data.get('book_path')

        if not book_path:
            return jsonify({'error': '未指定书籍'}), 400

        try:
            from core.reading_engine import ReadingEngine
            reading_engine = ReadingEngine(config)

            # 加载书籍
            book_data = reading_engine.load_book(book_path)
            book_info = reading_engine.get_book_info(book_data)

            return jsonify({
                'success': True,
                'book_info': book_info,
                'book_id': book_data['book_id']
            })

        except Exception as e:
            return jsonify({'error': f'加载书籍失败: {str(e)}'}), 500

    @app.route('/api/reading/<book_id>/<int:block_index>')
    def api_get_text_block(book_id, block_index):
        """获取文本块"""
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'error': '未登录'}), 401

        try:
            from core.reading_engine import ReadingEngine
            from cli.terminal_ui import find_book_file

            reading_engine = ReadingEngine(config)

            # 查找书籍文件
            book_path = find_book_file(book_id, config)
            if not book_path:
                return jsonify({'error': '未找到书籍文件'}), 404

            # 加载书籍
            book_data = reading_engine.load_book(book_path)

            # 获取文本块
            result = reading_engine.navigate_to_block(
                user_id, book_data, block_index)

            # 获取菜单选项
            menu_options = reading_engine.get_reading_menu(
                user_id, book_data, block_index)

            # 获取进度信息
            progress = reading_engine.get_reading_progress(user_id, book_data)

            return jsonify({
                'success': True,
                'content': result['content'],
                'mode': result['mode'],
                'message': result['message'],
                'fallback_level': result['fallback_level'],
                'menu_options': menu_options,
                'progress': progress,
                'block_info': result.get('block_info', {})
            })

        except Exception as e:
            return jsonify({'error': f'获取文本块失败: {str(e)}'}), 500

    @app.route('/api/switch_mode', methods=['POST'])
    def api_switch_mode():
        """切换阅读模式"""
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'error': '未登录'}), 401

        data = request.get_json()
        new_mode = data.get('mode')

        if new_mode not in ['ai_summary', 'original_text']:
            return jsonify({'error': '无效的阅读模式'}), 400

        try:
            from core.reading_engine import ReadingEngine
            reading_engine = ReadingEngine(config)

            success = reading_engine.switch_reading_mode(user_id, new_mode)

            return jsonify({
                'success': success,
                'new_mode': new_mode
            })

        except Exception as e:
            return jsonify({'error': f'切换模式失败: {str(e)}'}), 500

    @app.route('/api/status')
    def api_status():
        """API状态检查"""
        from core.ai_client import is_ai_available

        ai_status = is_ai_available(config["ai"]["api_url"])

        return jsonify({
            'system': 'ok',
            'ai_available': ai_status,
            'version': config['system']['version']
        })

    @app.route('/logout')
    def logout():
        """用户注销"""
        user_id = session.get('user_id')
        if user_id:
            # TODO: 实现用户数据备份
            from core.user_manager import backup_user_data
            backup_user_data(user_id, config)

        session.clear()
        return redirect('/')

    return app


def start_web_interface(config: Dict[str, Any], port: int = 5000) -> None:
    """启动Web界面"""
    app = create_app(config)

    print(f"启动Web服务器，端口: {port}")
    print(f"访问地址: http://localhost:{port}")

    try:
        app.run(host='0.0.0.0', port=port, debug=False)
    except KeyboardInterrupt:
        print("\nWeb服务器已停止。")
    except Exception as e:
        print(f"Web服务器启动失败: {e}")
