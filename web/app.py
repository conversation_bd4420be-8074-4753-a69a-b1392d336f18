#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Flask Web应用
"""

from flask import Flask, render_template, request, jsonify, session, redirect
from typing import Dict, Any


def create_app(config: Dict[str, Any]) -> Flask:
    """创建Flask应用"""
    app = Flask(__name__)
    app.secret_key = "reading_plan_secret_key_2024"  # 生产环境应使用随机密钥

    # 存储配置
    app.config['READING_PLAN_CONFIG'] = config

    @app.route('/')
    def index():
        """主页"""
        return render_template('index.html')

    @app.route('/login', methods=['GET', 'POST'])
    def login():
        """用户登录"""
        if request.method == 'POST':
            user_id = request.form.get('user_id')
            if user_id:
                session['user_id'] = user_id
                session['interface'] = 'web'
                return jsonify({'success': True, 'redirect': '/reading'})
            return jsonify({'success': False, 'message': '请输入用户名'})

        return render_template('login.html')

    @app.route('/reading')
    def reading():
        """阅读界面"""
        user_id = session.get('user_id')
        if not user_id:
            return redirect('/login')

        # TODO: 实现阅读界面逻辑
        return render_template('reading.html', user_id=user_id)

    @app.route('/api/status')
    def api_status():
        """API状态检查"""
        from core.ai_client import is_ai_available

        ai_status = is_ai_available(config["ai"]["api_url"])

        return jsonify({
            'system': 'ok',
            'ai_available': ai_status,
            'version': config['system']['version']
        })

    @app.route('/logout')
    def logout():
        """用户注销"""
        user_id = session.get('user_id')
        if user_id:
            # TODO: 实现用户数据备份
            from core.user_manager import backup_user_data
            backup_user_data(user_id, config)

        session.clear()
        return redirect('/')

    return app


def start_web_interface(config: Dict[str, Any], port: int = 5000) -> None:
    """启动Web界面"""
    app = create_app(config)

    print(f"启动Web服务器，端口: {port}")
    print(f"访问地址: http://localhost:{port}")

    try:
        app.run(host='0.0.0.0', port=port, debug=False)
    except KeyboardInterrupt:
        print("\nWeb服务器已停止。")
    except Exception as e:
        print(f"Web服务器启动失败: {e}")
