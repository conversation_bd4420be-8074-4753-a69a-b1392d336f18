{% extends "base.html" %}

{% block title %}阅读界面 - 柔性阅读计划{% endblock %}

{% block content %}
<div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
    <h2>欢迎，{{ user_id }}！</h2>
    <div>
        <a href="/logout" class="btn btn-secondary">注销</a>
    </div>
</div>

<div id="reading-container">
    <div class="alert alert-success">
        <h3>阅读功能开发中</h3>
        <p>以下功能正在开发中：</p>
        <ul>
            <li>书籍选择和管理</li>
            <li>AI辅助阅读模式</li>
            <li>断点续读功能</li>
            <li>阅读进度追踪</li>
            <li>认知雷达图显示</li>
        </ul>
    </div>
    
    <div style="margin-top: 30px;">
        <h3>当前状态</h3>
        <p>用户: {{ user_id }}</p>
        <p>界面: Web模式</p>
        <p>AI服务: <span id="ai-status">检查中...</span></p>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
// 检查AI服务状态
fetch('/api/status')
    .then(response => response.json())
    .then(data => {
        const statusElement = document.getElementById('ai-status');
        if (data.ai_available) {
            statusElement.textContent = '✓ 可用';
            statusElement.style.color = 'green';
        } else {
            statusElement.textContent = '✗ 不可用';
            statusElement.style.color = 'red';
        }
    })
    .catch(error => {
        document.getElementById('ai-status').textContent = '检查失败';
    });
</script>
{% endblock %}
