{% extends "base.html" %}

{% block title %}用户登录 - 柔性阅读计划{% endblock %}

{% block content %}
<h2>用户身份认证</h2>
<p>请选择您的身份以获得个性化的阅读体验：</p>

<form id="loginForm" method="post">
    <div class="form-group">
        <label for="user_id">选择用户：</label>
        <select id="user_id" name="user_id" required>
            <option value="">请选择...</option>
            <option value="老汉">老汉 (管理员)</option>
            <option value="啾啾">啾啾</option>
            <option value="妙妙">妙妙</option>
            <option value="测试">测试用户</option>
        </select>
    </div>
    
    <div class="form-group">
        <button type="submit" class="btn">登录</button>
        <a href="/" class="btn btn-secondary">返回首页</a>
    </div>
</form>

<div id="message" style="display: none;"></div>

{% endblock %}

{% block extra_js %}
<script>
document.getElementById('loginForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const messageDiv = document.getElementById('message');
    
    fetch('/login', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            messageDiv.className = 'alert alert-success';
            messageDiv.textContent = '登录成功，正在跳转...';
            messageDiv.style.display = 'block';
            
            setTimeout(() => {
                window.location.href = data.redirect;
            }, 1000);
        } else {
            messageDiv.className = 'alert alert-error';
            messageDiv.textContent = data.message || '登录失败';
            messageDiv.style.display = 'block';
        }
    })
    .catch(error => {
        messageDiv.className = 'alert alert-error';
        messageDiv.textContent = '网络错误，请重试';
        messageDiv.style.display = 'block';
    });
});
</script>
{% endblock %}
