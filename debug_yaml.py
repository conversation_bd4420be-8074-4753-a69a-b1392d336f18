#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试YAML解析问题
"""

import yaml


def debug_yaml_parsing():
    """调试YAML解析"""
    try:
        with open("柔性阅读计划.md", 'r', encoding='utf-8') as f:
            content = f.read()

        print("文件内容前500字符:")
        print(repr(content[:500]))
        print()

        # 检查是否有YAML Front Matter
        if content.startswith('---'):
            # 找到第二个---的位置
            first_end = content.find('\n---\n', 4)  # 从第4个字符开始查找
            if first_end == -1:
                first_end = content.find('\n---', 4)

            if first_end != -1:
                yaml_content = content[4:first_end].strip()  # 跳过开头的---
                markdown_content = content[first_end+4:].strip()  # 跳过结尾的---
                parts = [content[:4], yaml_content, markdown_content]
            else:
                parts = content.split('---', 2)
            print(f"分割后的部分数量: {len(parts)}")

            if len(parts) >= 3:
                yaml_content = parts[1].strip()
                print(f"YAML内容长度: {len(yaml_content)}")
                print("YAML内容前500字符:")
                print(repr(yaml_content[:500]))
                print()

                # 清理YAML内容中的注释行
                yaml_lines = []
                for line in yaml_content.split('\n'):
                    if line.strip() and not line.strip().startswith('#'):
                        if '#' in line:
                            line = line.split('#')[0].rstrip()
                        yaml_lines.append(line)

                cleaned_yaml = '\n'.join(yaml_lines)
                print(f"清理后的YAML长度: {len(cleaned_yaml)}")
                print("清理后的YAML前500字符:")
                print(repr(cleaned_yaml[:500]))
                print()

                # 尝试解析YAML
                try:
                    yaml_data = yaml.safe_load(cleaned_yaml)
                    print(f"YAML解析成功！")
                    print(
                        f"顶级键: {list(yaml_data.keys()) if yaml_data else 'None'}")

                    if 'userProfiles' in yaml_data:
                        print(f"用户配置数量: {len(yaml_data['userProfiles'])}")
                        for i, user in enumerate(yaml_data['userProfiles']):
                            print(
                                f"  用户{i+1}: {user.get('username', 'Unknown')}")
                    else:
                        print("未找到 userProfiles 键")

                except Exception as e:
                    print(f"YAML解析失败: {e}")

    except Exception as e:
        print(f"文件读取失败: {e}")


if __name__ == "__main__":
    debug_yaml_parsing()
