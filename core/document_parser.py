#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
文档解析器模块 - 支持PDF、EPUB、DOCX、TXT格式
"""

from utils.helpers import (
    load_json, save_json, calculate_text_hash, safe_filename
)
import os
import re
import hashlib
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path

# PDF解析 - 暂时禁用以避免依赖冲突
PDF_AVAILABLE = False

# EPUB解析
try:
    import ebooklib
    from ebooklib import epub
    EPUB_AVAILABLE = True
except ImportError:
    EPUB_AVAILABLE = False

# DOCX解析
try:
    from docx import Document
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False


class UnsupportedFormatError(Exception):
    """不支持的文件格式错误"""
    pass


class DocumentParseError(Exception):
    """文档解析错误"""
    pass


def get_cache_path(file_path: str, cache_dir: str) -> str:
    """生成缓存文件路径"""
    filename = safe_filename(os.path.basename(file_path))
    cache_filename = f"{filename}.json"
    return os.path.join(cache_dir, cache_filename)


def is_cache_valid(file_path: str, cache_path: str) -> bool:
    """检查缓存是否有效"""
    if not os.path.exists(cache_path):
        return False

    # 检查缓存是否新于原文件
    return os.path.getmtime(cache_path) > os.path.getmtime(file_path)


def parse_document(file_path: str, config: Dict[str, Any]) -> Dict[str, Any]:
    """统一文档解析接口"""
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")

    # 检查缓存
    cache_dir = config["system"]["cache_dir"]
    cache_path = get_cache_path(file_path, cache_dir)

    if is_cache_valid(file_path, cache_path):
        cached_data = load_json(cache_path)
        if cached_data:
            print(f"从缓存加载: {os.path.basename(file_path)}")
            return cached_data

    # 解析文档
    file_ext = Path(file_path).suffix.lower()

    try:
        if file_ext == '.pdf':
            content = parse_pdf(file_path)
        elif file_ext == '.epub':
            content = parse_epub(file_path)
        elif file_ext == '.docx':
            content = parse_docx(file_path)
        elif file_ext in ['.txt', '.md']:
            content = parse_txt(file_path)
        else:
            raise UnsupportedFormatError(f"不支持的文件格式: {file_ext}")

        # 添加元数据
        content.update({
            'file_path': file_path,
            'file_name': os.path.basename(file_path),
            'file_size': os.path.getsize(file_path),
            'file_format': file_ext,
            'parsed_at': __import__('time').time()
        })

        # 保存缓存
        os.makedirs(cache_dir, exist_ok=True)
        save_json(cache_path, content)

        print(f"解析完成: {os.path.basename(file_path)}")
        return content

    except Exception as e:
        raise DocumentParseError(f"解析文档失败 {file_path}: {str(e)}")


def parse_pdf(file_path: str) -> Dict[str, Any]:
    """解析PDF文件"""
    if not PDF_AVAILABLE:
        raise ImportError("PDF解析库未安装，请安装 PyPDF2 和 pdfplumber")

    content = {
        'title': '',
        'author': '',
        'pages': [],
        'total_pages': 0,
        'text_blocks': [],
        'metadata': {}
    }

    try:
        # 使用pdfplumber提取文本（更准确）
        with pdfplumber.open(file_path) as pdf:
            content['total_pages'] = len(pdf.pages)

            for page_num, page in enumerate(pdf.pages, 1):
                page_text = page.extract_text() or ""

                content['pages'].append({
                    'page_number': page_num,
                    'text': page_text,
                    'char_count': len(page_text)
                })

        # 使用PyPDF2提取元数据
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                if pdf_reader.metadata:
                    content['title'] = pdf_reader.metadata.get(
                        '/Title', '') or ''
                    content['author'] = pdf_reader.metadata.get(
                        '/Author', '') or ''
                    content['metadata'] = {
                        'creator': pdf_reader.metadata.get('/Creator', ''),
                        'producer': pdf_reader.metadata.get('/Producer', ''),
                        'subject': pdf_reader.metadata.get('/Subject', ''),
                        'creation_date': str(pdf_reader.metadata.get('/CreationDate', ''))
                    }
        except:
            pass  # 元数据提取失败不影响主要功能

        # 生成文本块
        content['text_blocks'] = generate_text_blocks(content['pages'])

    except Exception as e:
        raise DocumentParseError(f"PDF解析失败: {str(e)}")

    return content


def parse_epub(file_path: str) -> Dict[str, Any]:
    """解析EPUB文件"""
    if not EPUB_AVAILABLE:
        raise ImportError("EPUB解析库未安装，请安装 ebooklib")

    content = {
        'title': '',
        'author': '',
        'chapters': [],
        'total_chapters': 0,
        'text_blocks': [],
        'metadata': {}
    }

    try:
        book = epub.read_epub(file_path)

        # 提取元数据
        content['title'] = book.get_metadata(
            'DC', 'title')[0][0] if book.get_metadata('DC', 'title') else ''
        content['author'] = book.get_metadata(
            'DC', 'creator')[0][0] if book.get_metadata('DC', 'creator') else ''
        content['metadata'] = {
            'language': book.get_metadata('DC', 'language')[0][0] if book.get_metadata('DC', 'language') else '',
            'publisher': book.get_metadata('DC', 'publisher')[0][0] if book.get_metadata('DC', 'publisher') else '',
            'identifier': book.get_metadata('DC', 'identifier')[0][0] if book.get_metadata('DC', 'identifier') else ''
        }

        # 提取章节内容
        chapter_num = 0
        for item in book.get_items():
            if item.get_type() == ebooklib.ITEM_DOCUMENT:
                chapter_num += 1

                # 提取HTML内容并转换为纯文本
                html_content = item.get_content().decode('utf-8')
                text_content = extract_text_from_html(html_content)

                content['chapters'].append({
                    'chapter_number': chapter_num,
                    'title': item.get_name(),
                    'text': text_content,
                    'char_count': len(text_content)
                })

        content['total_chapters'] = chapter_num

        # 生成文本块
        content['text_blocks'] = generate_text_blocks(content['chapters'])

    except Exception as e:
        raise DocumentParseError(f"EPUB解析失败: {str(e)}")

    return content


def parse_docx(file_path: str) -> Dict[str, Any]:
    """解析DOCX文件"""
    if not DOCX_AVAILABLE:
        raise ImportError("DOCX解析库未安装，请安装 python-docx")

    content = {
        'title': '',
        'author': '',
        'paragraphs': [],
        'total_paragraphs': 0,
        'text_blocks': [],
        'metadata': {}
    }

    try:
        doc = Document(file_path)

        # 提取元数据
        core_props = doc.core_properties
        content['title'] = core_props.title or ''
        content['author'] = core_props.author or ''
        content['metadata'] = {
            'subject': core_props.subject or '',
            'keywords': core_props.keywords or '',
            'category': core_props.category or '',
            'created': str(core_props.created) if core_props.created else '',
            'modified': str(core_props.modified) if core_props.modified else ''
        }

        # 提取段落内容
        for para_num, paragraph in enumerate(doc.paragraphs, 1):
            text = paragraph.text.strip()
            if text:  # 跳过空段落
                content['paragraphs'].append({
                    'paragraph_number': para_num,
                    'text': text,
                    'char_count': len(text)
                })

        content['total_paragraphs'] = len(content['paragraphs'])

        # 生成文本块
        content['text_blocks'] = generate_text_blocks(content['paragraphs'])

    except Exception as e:
        raise DocumentParseError(f"DOCX解析失败: {str(e)}")

    return content


def parse_txt(file_path: str) -> Dict[str, Any]:
    """解析TXT文件"""
    content = {
        'title': os.path.splitext(os.path.basename(file_path))[0],
        'author': '',
        'lines': [],
        'total_lines': 0,
        'text_blocks': [],
        'metadata': {}
    }

    try:
        # 尝试多种编码
        encodings = ['utf-8', 'gbk', 'gb2312', 'big5', 'latin1']
        text_content = None

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as file:
                    text_content = file.read()
                break
            except UnicodeDecodeError:
                continue

        if text_content is None:
            raise DocumentParseError("无法识别文件编码")

        # 按行分割
        lines = text_content.split('\n')
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if line:  # 跳过空行
                content['lines'].append({
                    'line_number': line_num,
                    'text': line,
                    'char_count': len(line)
                })

        content['total_lines'] = len(content['lines'])

        # 生成文本块
        content['text_blocks'] = generate_text_blocks(content['lines'])

    except Exception as e:
        raise DocumentParseError(f"TXT解析失败: {str(e)}")

    return content


def extract_text_from_html(html_content: str) -> str:
    """从HTML内容中提取纯文本"""
    # 简单的HTML标签清理
    import re

    # 移除HTML标签
    text = re.sub(r'<[^>]+>', '', html_content)

    # 解码HTML实体
    text = text.replace('&nbsp;', ' ')
    text = text.replace('&lt;', '<')
    text = text.replace('&gt;', '>')
    text = text.replace('&amp;', '&')
    text = text.replace('&quot;', '"')

    # 清理多余的空白字符
    text = re.sub(r'\s+', ' ', text)
    text = text.strip()

    return text


def generate_text_blocks(sections: List[Dict[str, Any]], block_size: int = 2000) -> List[Dict[str, Any]]:
    """将文档内容分割为文本块"""
    text_blocks = []
    current_block = ""
    block_num = 1

    for section in sections:
        section_text = section.get('text', '')

        # 如果当前块加上新段落超过限制，先保存当前块
        if current_block and len(current_block) + len(section_text) > block_size:
            if current_block.strip():
                text_blocks.append({
                    'block_number': block_num,
                    'text': current_block.strip(),
                    'char_count': len(current_block.strip()),
                    'hash': calculate_text_hash(current_block.strip())
                })
                block_num += 1
            current_block = ""

        # 添加当前段落到块中
        if current_block:
            current_block += "\n\n" + section_text
        else:
            current_block = section_text

    # 保存最后一个块
    if current_block.strip():
        text_blocks.append({
            'block_number': block_num,
            'text': current_block.strip(),
            'char_count': len(current_block.strip()),
            'hash': calculate_text_hash(current_block.strip())
        })

    return text_blocks


def get_supported_formats() -> List[str]:
    """获取支持的文件格式列表"""
    formats = ['.txt', '.md']  # TXT和MD总是支持的

    if PDF_AVAILABLE:
        formats.append('.pdf')

    if EPUB_AVAILABLE:
        formats.append('.epub')

    if DOCX_AVAILABLE:
        formats.append('.docx')

    return formats


def validate_document(file_path: str) -> Tuple[bool, str]:
    """验证文档是否可以解析"""
    if not os.path.exists(file_path):
        return False, "文件不存在"

    file_ext = Path(file_path).suffix.lower()
    supported_formats = get_supported_formats()

    if file_ext not in supported_formats:
        return False, f"不支持的文件格式: {file_ext}"

    # 检查文件大小（限制为100MB）
    file_size = os.path.getsize(file_path)
    if file_size > 100 * 1024 * 1024:
        return False, "文件过大（超过100MB）"

    return True, "文档有效"
