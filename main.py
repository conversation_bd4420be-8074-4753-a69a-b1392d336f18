#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
主入口文件
"""

from web.app import start_web_interface
from cli.terminal_ui import start_cli_interface
from config.settings import load_config
import argparse
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def main():
    """主入口函数"""
    parser = argparse.ArgumentParser(description="柔性阅读计划 v6.0")
    parser.add_argument("--web", action="store_true", help="启动Web界面")
    parser.add_argument("--config", type=str, help="指定配置文件路径")
    parser.add_argument("--port", type=int, default=5000, help="Web界面端口号")

    args = parser.parse_args()

    # 加载配置
    config = load_config(args.config)

    # 启动相应界面
    if args.web:
        print("启动Web界面...")
        start_web_interface(config, port=args.port)
    else:
        print("启动命令行界面...")
        start_cli_interface(config)


if __name__ == "__main__":
    main()
